-- Data Extractor for Bubble Infinity
-- This script extracts all available game data for analysis

local DataExtractor = {}

-- Services
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local LocalPlayer = Players.LocalPlayer

-- Required modules
local LocalData = require(ReplicatedStorage.Client.Framework.Services.LocalData)
local Storage = require(ReplicatedStorage.Shared.Data.Gum)
local CodesModule = require(ReplicatedStorage.Shared.Data.Codes)
local EggsData = require(ReplicatedStorage.Shared.Data.Eggs)
local EnchantsData = require(ReplicatedStorage.Shared.Data.Enchants)
local PetsData = pcall(function() return require(ReplicatedStorage.Shared.Data.Pets) end) and require(ReplicatedStorage.Shared.Data.Pets) or {}
local CurrencyData = pcall(function() return require(ReplicatedStorage.Shared.Data.Currency) end) and require(ReplicatedStorage.Shared.Data.Currency) or {}
local Constants = pcall(function() return require(ReplicatedStorage.Shared.Constants) end) and require(ReplicatedStorage.Shared.Constants) or {}

-- Extract all player data
function DataExtractor:GetPlayerData()
    local playerData = LocalData:Get()
    if not playerData then return {error = "Could not retrieve player data"} end

    return playerData
end

-- Extract all pets data
function DataExtractor:GetPetsData()
    local playerData = self:GetPlayerData()
    if not playerData then return {error = "Could not retrieve player data"} end

    local petsData = {
        All = playerData.Pets or {},
        Equipped = {},
        EquippedIds = playerData.EquippedPets or {},
        PetsModule = PetsData
    }

    -- Populate equipped pets with full data
    if playerData.EquippedPets then
        for _, petId in pairs(playerData.EquippedPets) do
            for _, pet in pairs(playerData.Pets) do
                if pet.Id == petId then
                    table.insert(petsData.Equipped, pet)
                    break
                end
            end
        end
    end

    return petsData
end

-- Extract all currency data
function DataExtractor:GetCurrencyData()
    local playerData = self:GetPlayerData()
    if not playerData then return {error = "Could not retrieve player data"} end

    return {
        Coins = playerData.Coins or 0,
        Gems = playerData.Gems or 0,
        Bubble = playerData.Bubble or {},
        CurrencyModule = CurrencyData
    }
end

-- Extract all egg/hatching data
function DataExtractor:GetEggData()
    local playerData = self:GetPlayerData()
    if not playerData then return {error = "Could not retrieve player data"} end

    return {
        Opened = playerData.EggsOpened or {},
        TotalOpened = playerData.Stats and playerData.Stats.TotalEggsOpened or 0,
        EggsModule = EggsData
    }
end

-- Extract all area/island data
function DataExtractor:GetAreaData()
    local playerData = self:GetPlayerData()
    if not playerData then return {error = "Could not retrieve player data"} end

    return {
        Unlocked = playerData.AreasUnlocked or {},
        Current = playerData.CurrentArea
    }
end

-- Extract all upgrade data
function DataExtractor:GetUpgradeData()
    local playerData = self:GetPlayerData()
    if not playerData then return {error = "Could not retrieve player data"} end

    return {
        Regular = playerData.Upgrades or {},
        Mastery = playerData.MasteryUpgrades or {}
    }
end

-- Extract all achievement data
function DataExtractor:GetAchievementData()
    local playerData = self:GetPlayerData()
    if not playerData then return {error = "Could not retrieve player data"} end

    return {
        Completed = playerData.Achievements or {},
        Progress = playerData.AchievementProgress or {}
    }
end

-- Extract all code redemption data
function DataExtractor:GetCodeData()
    local playerData = self:GetPlayerData()
    if not playerData then return {error = "Could not retrieve player data"} end

    return {
        Redeemed = playerData.Redeemed or {},
        CodesModule = CodesModule
    }
end

-- Extract all enchant data
function DataExtractor:GetEnchantData()
    local playerData = self:GetPlayerData()
    if not playerData then return {error = "Could not retrieve player data"} end

    return {
        Discovered = playerData.DiscoveredEnchants or {},
        EnchantsModule = EnchantsData
    }
end

-- Extract all pass/gamepass data
function DataExtractor:GetPassData()
    local playerData = self:GetPlayerData()
    if not playerData then return {error = "Could not retrieve player data"} end

    return playerData.Passes or {}
end

-- Extract all stats data
function DataExtractor:GetStatsData()
    local playerData = self:GetPlayerData()
    if not playerData then return {error = "Could not retrieve player data"} end

    return playerData.Stats or {}
end

-- Extract all powerup data
function DataExtractor:GetPowerupData()
    local playerData = self:GetPlayerData()
    if not playerData then return {error = "Could not retrieve player data"} end

    return playerData.Powerups or {}
end

-- Extract all inventory data
function DataExtractor:GetInventoryData()
    local playerData = self:GetPlayerData()
    if not playerData then return {error = "Could not retrieve player data"} end

    return {
        Items = playerData.Inventory or {},
        Equipped = playerData.EquippedItems or {}
    }
end

-- Extract all time-related data
function DataExtractor:GetTimeData()
    local playerData = self:GetPlayerData()
    if not playerData then return {error = "Could not retrieve player data"} end

    return {
        LastLogin = playerData.LastLogin,
        PlayTime = playerData.PlayTime,
        JoinDate = playerData.JoinDate
    }
end

-- Extract all settings/preferences
function DataExtractor:GetSettingsData()
    local playerData = self:GetPlayerData()
    if not playerData then return {error = "Could not retrieve player data"} end

    return playerData.Settings or {}
end

-- Extract all quest data
function DataExtractor:GetQuestData()
    local playerData = self:GetPlayerData()
    if not playerData then return {error = "Could not retrieve player data"} end

    return {
        Active = playerData.Quests or {},
        Completed = playerData.CompletedQuests or {}
    }
end

-- Extract all game constants
function DataExtractor:GetConstantsData()
    return Constants
end

-- Extract all game module data
function DataExtractor:GetGameModules()
    return {
        Gum = Storage,
        Eggs = EggsData,
        Enchants = EnchantsData,
        Codes = CodesModule,
        Pets = PetsData,
        Currency = CurrencyData,
        Constants = Constants
    }
end

-- Get all data at once
function DataExtractor:GetAllData()
    return {
        Player = self:GetPlayerData(),
        Pets = self:GetPetsData(),
        Currency = self:GetCurrencyData(),
        Eggs = self:GetEggData(),
        Areas = self:GetAreaData(),
        Upgrades = self:GetUpgradeData(),
        Achievements = self:GetAchievementData(),
        Codes = self:GetCodeData(),
        Enchants = self:GetEnchantData(),
        Passes = self:GetPassData(),
        Stats = self:GetStatsData(),
        Powerups = self:GetPowerupData(),
        Inventory = self:GetInventoryData(),
        Time = self:GetTimeData(),
        Settings = self:GetSettingsData(),
        Quests = self:GetQuestData(),
        Constants = self:GetConstantsData(),
        GameModules = self:GetGameModules()
    }
end

-- Save all data to a file
function DataExtractor:SaveDataToFile(filename)
    local allData = self:GetAllData()
    local HttpService = game:GetService("HttpService")

    local success, jsonData = pcall(function()
        return HttpService:JSONEncode(allData)
    end)

    if not success then
        warn("Failed to encode data to JSON")
        return false
    end

    -- Try to save using writefile if available (exploit-dependent)
    if writefile then
        filename = filename or "BubbleInfinityData_" .. os.time() .. ".json"
        writefile(filename, jsonData)
        return filename
    else
        warn("writefile function not available in this exploit")
        return false
    end
end

-- Print specific data to output
function DataExtractor:PrintData(dataType)
    local data

    if dataType == "Player" then
        data = self:GetPlayerData()
    elseif dataType == "Pets" then
        data = self:GetPetsData()
    elseif dataType == "Currency" then
        data = self:GetCurrencyData()
    elseif dataType == "Eggs" then
        data = self:GetEggData()
    elseif dataType == "Areas" then
        data = self:GetAreaData()
    elseif dataType == "Upgrades" then
        data = self:GetUpgradeData()
    elseif dataType == "Achievements" then
        data = self:GetAchievementData()
    elseif dataType == "Codes" then
        data = self:GetCodeData()
    elseif dataType == "Enchants" then
        data = self:GetEnchantData()
    elseif dataType == "Passes" then
        data = self:GetPassData()
    elseif dataType == "Stats" then
        data = self:GetStatsData()
    elseif dataType == "Powerups" then
        data = self:GetPowerupData()
    elseif dataType == "Inventory" then
        data = self:GetInventoryData()
    elseif dataType == "Time" then
        data = self:GetTimeData()
    elseif dataType == "Settings" then
        data = self:GetSettingsData()
    elseif dataType == "Quests" then
        data = self:GetQuestData()
    elseif dataType == "Constants" then
        data = self:GetConstantsData()
    elseif dataType == "GameModules" then
        data = self:GetGameModules()
    else
        data = self:GetAllData()
    end

    -- Print data to output (limited by output size)
    local HttpService = game:GetService("HttpService")
    local success, jsonData = pcall(function()
        return HttpService:JSONEncode(data)
    end)

    if success then
        print("--- " .. (dataType or "All Data") .. " ---")
        print(jsonData)
        return true
    else
        warn("Failed to encode data to JSON")
        return false
    end
end

-- Get equipped pets with their enchants for UI display
function DataExtractor:GetEquippedPetsWithEnchants()
    local playerData = self:GetPlayerData()
    local equippedPetsInfo = {}
    local equippedPetsDropdown = {}

    -- Check if data has the required structure
    if not playerData or not playerData.Teams or not playerData.Pets or not playerData.Pets.All then
        return equippedPetsInfo, equippedPetsDropdown
    end

    -- Get the active team index (default to 1 if not specified)
    local activeTeamIndex = playerData.TeamEquipped or 1

    -- Get the active team
    local activeTeam = playerData.Teams[activeTeamIndex]
    if not activeTeam or not activeTeam.Pets then
        return equippedPetsInfo, equippedPetsDropdown
    end

    -- Get the equipped pet IDs from the active team
    local equippedPetIds = activeTeam.Pets

    -- Find each equipped pet in the All pets array
    for _, petId in pairs(equippedPetIds) do
        for _, pet in pairs(playerData.Pets.All) do
            if pet.Id == petId then
                -- Get enchant info
                local enchantInfo = ""
                if pet.Enchants then
                    for _, enchant in pairs(pet.Enchants) do
                        if enchantInfo ~= "" then enchantInfo = enchantInfo .. ", " end
                        enchantInfo = enchantInfo .. enchant.Id .. " " .. enchant.Level
                    end
                end

                if enchantInfo == "" then enchantInfo = "No enchants" end

                local displayName = pet.Name .. " - " .. enchantInfo
                table.insert(equippedPetsDropdown, displayName)
                equippedPetsInfo[displayName] = pet
                break
            end
        end
    end

    return equippedPetsInfo, equippedPetsDropdown
end

-- Copy all data to clipboard
function DataExtractor:CopyAllDataToClipboard()
    local allData = self:GetAllData()
    local HttpService = game:GetService("HttpService")

    local success, jsonData = pcall(function()
        return HttpService:JSONEncode(allData)
    end)

    if success then
        if setclipboard then
            setclipboard(jsonData)
            print("All game data copied to clipboard! Data size: " .. string.len(jsonData) .. " characters")
            return true
        else
            warn("setclipboard function not available in this exploit")
            return false
        end
    else
        warn("Failed to encode data to JSON")
        return false
    end
end

-- Copy raw player data to clipboard (no formatting)
function DataExtractor:CopyRawPlayerDataToClipboard()
    local playerData = LocalData:Get()
    local HttpService = game:GetService("HttpService")

    local success, jsonData = pcall(function()
        return HttpService:JSONEncode(playerData)
    end)

    if success then
        if setclipboard then
            setclipboard(jsonData)
            print("Raw player data copied to clipboard! Data size: " .. string.len(jsonData) .. " characters")
            return true
        else
            warn("setclipboard function not available in this exploit")
            return false
        end
    else
        warn("Failed to encode data to JSON")
        return false
    end
end


function DataExtractor:CopyModuleDataToClipboard(moduleName)
    local moduleData

    if moduleName == "Gum" then
        moduleData = Storage
    elseif moduleName == "Eggs" then
        moduleData = EggsData
    elseif moduleName == "Enchants" then
        moduleData = EnchantsData
    elseif moduleName == "Codes" then
        moduleData = CodesModule
    elseif moduleName == "Pets" then
        moduleData = PetsData
    elseif moduleName == "Currency" then
        moduleData = CurrencyData
    elseif moduleName == "Constants" then
        moduleData = Constants
    else
        warn("Unknown module: " .. moduleName)
        return false
    end

    local HttpService = game:GetService("HttpService")

    local success, jsonData = pcall(function()
        return HttpService:JSONEncode(moduleData)
    end)

    if success then
        if setclipboard then
            setclipboard(jsonData)
            print(moduleName .. " module data copied to clipboard! Data size: " .. string.len(jsonData) .. " characters")
            return true
        else
            warn("setclipboard function not available in this exploit")
            return false
        end
    else
        warn("Failed to encode data to JSON")
        return false
    end
end


DataExtractor:CopyAllDataToClipboard()
