local v1 = game:GetService("ReplicatedStorage")
require(v1.Shared.Types)
local u2 = require(v1.Shared.Utils.Stats.ItemUtil)
local u3 = require(v1.Shared.Framework.Utilities.String.FormatSuffix)
local u4 = require(v1.Shared.Framework.Utilities.String.FormatPlural)
local u5 = require(v1.Shared.Constants)
local u6 = require(v1.Shared.Data.Flavors)
local u7 = require(v1.Shared.Data.Eggs)
require(v1.Shared.Data.Pets)
local u8 = require(v1.Shared.Data.Gum)
local u9 = require(v1.Shared.Framework.Utilities.Table.DeepCopy)
local u10 = require(v1.Shared.Framework.Utilities.Math.Time)
local u11 = require(v1.Shared.Data.Currency)
local u14 = {
    ["GetRequirement"] = function(_, p12) --[[Function name: GetRequirement, line 32]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        local v13 = p12.Amount
        return p12.Type ~= "Collect" and (v13 or 1) or u2:GetAmount(p12.Item)
    end
}
local u15 = {
    ["Common"] = "rbxassetid://104698246380703",
    ["Unique"] = "rbxassetid://87473851841077",
    ["Rare"] = "rbxassetid://95997927689265",
    ["Epic"] = "rbxassetid://76555595934303",
    ["Legendary"] = "rbxassetid://139856918486956",
    ["Secret"] = "rbxassetid://72547722153444"
}
function u14.FormatTask(_, p16) --[[Anonymous function at line 56]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u4
        [3] = u3
        [4] = u10
    --]]
    if p16.Type == "Collect" then
        local v17 = u2:GetAmount(p16.Item)
        local v18 = u2:GetName(p16.Item)
        local v19
        if v17 > 1 then
            v19 = u4(v18)
        else
            v19 = v18
        end
        if p16.Item.Type == "Currency" then
            v19 = u2:GetName(p16.Item)
        elseif p16.Item.Type == "Potion" then
            v19 = ("%* %*"):format(v18, v17 <= 1 and "Potion" or u4("Potion"))
        end
        return ("Collect %* %*"):format(u3(v17), v19)
    elseif p16.Type == "Hatch" then
        local v20 = p16.Amount
        if p16.Egg then
            local v21 = "Hatch %* %*"
            local v22 = u3(p16.Amount)
            local v23 = p16.Egg
            if v20 > 1 then
                v23 = u4(v23)
            end
            return v21:format(v22, v23)
        end
        if not (p16.Name or (p16.Shiny or (p16.Rarity or p16.Mythic))) then
            return ("Hatch %* %*"):format(u3(p16.Amount), v20 <= 1 and "Egg" or u4("Egg"))
        end
        local v24 = ""
        local v25
        if p16.Name then
            v25 = p16.Name
        else
            if p16.Mythic then
                v24 = v24 .. "Mythic "
            end
            if p16.Rarity then
                v24 = v24 .. p16.Rarity .. " "
            end
            v25 = v24 .. "Pet"
        end
        local v26 = "Hatch %* %*%*"
        local v27 = u3(v20)
        local v28 = p16.Shiny and "Shiny " or ""
        if v20 > 1 then
            v25 = u4(v25)
        end
        return v26:format(v27, v28, v25)
    elseif p16.Type == "Bubbles" then
        return ("Blow %* Bubbles"):format((u3(p16.Amount)))
    elseif p16.Type == "Invite" then
        return ("Invite %* %*"):format(u3(p16.Amount), p16.Amount <= 1 and "Friend" or u4("Friend"))
    elseif p16.Type == "Purchase" then
        if p16.Purchased.Type == "Gum" then
            return ("Purchase %*"):format(p16.Purchased.Name)
        else
            return ("Purchase %* %*"):format(p16.Purchased.Name, p16.Purchased.Type)
        end
    elseif p16.Type == "Group" then
        return "Join Rumble Studios Group!"
    elseif p16.Type == "Discord" then
        return "Verify your Discord!"
    elseif p16.Type == "Playtime" then
        return ("Play for %*"):format((u10.formatRealTime(p16.Amount)))
    elseif p16.Type == "Sell" then
        return p16.Amount == 1 and "Sell your bubble!" or ("Sell %* Bubbles"):format((u3(p16.Amount)))
    else
        return p16.Type ~= "AreaUnlock" and "Unknown" or ("Unlock %*%*"):format(string.find("the", string.lower(p16.Area)) and "" or "the ", p16.Area)
    end
end
function u14.IsComplete(_, p29) --[[Anonymous function at line 134]]
    --[[
    Upvalues:
        [1] = u14
    --]]
    local v30 = 0
    for v31, v32 in p29.Tasks do
        if u14:GetRequirement(v32) == p29.Progress[v31] then
            v30 = v30 + 1
        end
    end
    return v30 == #p29.Tasks
end
function u14.FindById(_, p33, p34) --[[Anonymous function at line 147]]
    local v35 = nil
    for _, v36 in p33.Quests do
        if v36.Id == p34 then
            return v36
        end
    end
    return v35
end
function u14.Remove(_, p37, p38) --[[Anonymous function at line 163]]
    local v39 = false
    for v40 = #p37.Quests, 1, -1 do
        if p38(p37.Quests[v40]) == true then
            table.remove(p37.Quests, v40)
            v39 = true
        end
    end
    return v39
end
function u14.Add(_, p41, p42) --[[Anonymous function at line 183]]
    --[[
    Upvalues:
        [1] = u9
    --]]
    local v43 = true
    for _, v44 in p41.Quests do
        if v44.Id == p42.Id then
            v43 = false
            break
        end
    end
    if v43 then
        local v45 = u9(p42)
        for v46, v47 in v45.Tasks do
            if v47.Type == "Purchase" then
                local v48 = false
                local v49
                if v47.Purchased.Type == "Gum" then
                    v49 = (p41.Gum[v47.Purchased.Name] or (p41.Passes.VIP or p41.Passes["Infinity Gum"])) and true or v48
                else
                    v49 = v47.Purchased.Type == "Flavor" and (p41.Flavors[v47.Purchased.Name] or p41.Passes.VIP) and true or v48
                end
                if v49 then
                    v45.Progress[v46] = 1
                end
            elseif v47.Type == "AreaUnlock" and p41.AreasUnlocked[v47.Area] then
                v45.Progress[v46] = 1
            end
        end
        local v50 = p41.Quests
        table.insert(v50, v45)
    end
    return v43
end
function u14.CompareTask(_, p51, p52, p53) --[[Anonymous function at line 227]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    if p51.Type == "Bubbles" and p52.Type == "Bubbles" then
        return (not p53 or p51.Amount == p52.Amount) and true or false
    elseif p51.Type == "Collect" and p52.Type == "Collect" then
        return u2:Compare(p51.Item, p52.Item, p53)
    elseif p51.Type == "Purchase" and p52.Type == "Purchase" then
        if p51.Purchased.Type == p52.Purchased.Type then
            return p51.Purchased.Name == p52.Purchased.Name
        else
            return false
        end
    elseif p51.Type == "Hatch" and p52.Type == "Hatch" then
        if p53 and p51.Amount ~= p52.Amount then
            return false
        elseif p51.Shiny or (p51.Mythic or (p51.Name or (p51.Rarity or p51.Egg))) then
            if p51.Rarity and p51.Rarity ~= p52.Rarity then
                return false
            elseif p51.Name and p51.Name ~= p52.Name then
                return false
            elseif p51.Shiny and p51.Shiny ~= p52.Shiny then
                return false
            elseif p51.Mythic and p51.Mythic ~= p52.Mythic then
                return false
            else
                return (not p51.Egg or p51.Egg == p52.Egg) and true or false
            end
        else
            return true
        end
    elseif p51.Type == "Invite" and p52.Type == "Invite" then
        return (not p53 or p51.Amount == p52.Amount) and true or false
    elseif p51.Type == "Group" and p52.Type == "Group" then
        return true
    elseif p51.Type == "Playtime" and p52.Type == "Playtime" then
        return (not p53 or p51.Amount == p52.Amount) and true or false
    elseif p51.Type == "Sell" and p52.Type == "Sell" then
        return (not p53 or p51.Amount ~= p52.Amount) and true or false
    elseif p51.Type == "AreaUnlock" and p52.Type == "AreaUnlock" then
        return p51.Area == p52.Area
    else
        return p51.Type == "Discord" and p52.Type == "Discord"
    end
end
function u14.Compare(_, p54, p55) --[[Anonymous function at line 303]]
    --[[
    Upvalues:
        [1] = u14
        [2] = u2
    --]]
    if #p54.Tasks ~= #p55.Tasks or #p54.Rewards ~= #p55.Rewards then
        return false
    end
    for v56, v57 in p54.Tasks do
        if not u14:CompareTask(v57, p55.Tasks[v56], true) then
            return false
        end
    end
    for v58, v59 in p54.Rewards do
        if not u2:Compare(v59, p55.Rewards[v58], true) then
            return false
        end
    end
    return true
end
function u14.UpdateTaskIcon(_, p60, p61) --[[Anonymous function at line 328]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u2
        [3] = u7
        [4] = u15
        [5] = u8
        [6] = u11
        [7] = u5
    --]]
    if p61.Type == "Bubbles" then
        p60.Label.Image = u6["Bubble Gum"].Image
        return
    elseif p61.Type == "Collect" then
        u2:UpdateIcon(p60, p61.Item)
    elseif p61.Type == "Hatch" then
        if p61.Egg then
            p60.Label.Image = u7[p61.Egg].Image
            return
        end
        if not (p61.Name or (p61.Shiny or (p61.Rarity or p61.Mythic))) then
            p60.Label.Image = u7["Common Egg"].Image
            return
        end
        if p61.Name then
            u2:UpdateIcon(p60, {
                ["Type"] = "Pet",
                ["Name"] = p61.Name,
                ["Mythic"] = p61.Mythic,
                ["Shiny"] = p61.Shiny,
                ["Amount"] = p61.Amount
            })
            return
        end
        if p61.Mythic then
            p60.Label.Image = "rbxassetid://74927583442057"
            return
        end
        if p61.Shiny then
            p60.Label.Image = "rbxassetid://73209196936182"
            return
        end
        if p61.Rarity then
            p60.Label.Image = u15[p61.Rarity]
            return
        end
    elseif p61.Type == "Purchase" then
        if p61.Purchased.Type == "Gum" then
            p60.Label.Image = u8[p61.Purchased.Name].Image
            return
        end
        if p61.Purchased.Type == "Flavor" then
            p60.Label.Image = u6[p61.Purchased.Name].Image
            return
        end
    else
        if p61.Type == "Sell" then
            p60.Label.Image = u11.Coins.Image
            return
        end
        if p61.Type == "AreaUnlock" then
            p60.Label.Image = "rbxassetid://115711155808064"
            return
        end
        if p61.Type == "Playtime" then
            p60.Label.Image = "rbxassetid://84730759262323"
            return
        end
        p60.Label.Image = u5.UnknownImage
    end
end
function u14.UpdateIcon(_, p62, p63) --[[Anonymous function at line 383]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u14
    --]]
    if p63.Icon == u5.UnknownImage then
        u14:UpdateTaskIcon(p62, p63.Tasks[1])
    else
        p62.Label.Image = p63.Icon
    end
end
return u14
