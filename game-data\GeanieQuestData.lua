local v1 = game:GetService("ReplicatedStorage")
require(v1.Shared.Types)
local u2 = require(v1.Shared.Framework.Utilities.Math.GetRandomWeightedItem)
require(v1.Shared.Data.Currency)
local v3 = require(v1.Shared.Data.Builders.LootPoolBuilder)
local u4 = require(v1.Shared.Utils.Stats.QuestUtil)
local u5 = require(v1.Shared.Data.Builders.QuestBuilder)
local u6 = require(v1.Shared.Utils.Stats.ItemUtil)
local u7 = require(v1.Shared.Utils.Stats.SeasonUtil)
local u8 = require(v1.Shared.Data.Quests.SeasonChallenges)
local u9 = {
    {
        ["Item"] = 1,
        ["Chance"] = 40
    },
    {
        ["Item"] = 2,
        ["Chance"] = 30
    },
    {
        ["Item"] = 3,
        ["Chance"] = 20
    },
    {
        ["Item"] = 4,
        ["Chance"] = 10
    }
}
local u10 = {
    {
        ["Item"] = 2,
        ["Chance"] = 60
    },
    {
        ["Item"] = 3,
        ["Chance"] = 40
    }
}
local u11 = v3.new():Add(5, {
    ["Type"] = "Powerup",
    ["Name"] = "Spin Ticket",
    ["Amount"] = 1
}):Add(5, {
    ["Type"] = "Powerup",
    ["Name"] = "Golden Key",
    ["Amount"] = 5
}):Add(4, {
    ["Type"] = "Powerup",
    ["Name"] = "Reroll Orb",
    ["Amount"] = 20
}):Add(4, {
    ["Type"] = "Powerup",
    ["Name"] = "Power Orb",
    ["Amount"] = 1
}):Add(10, {
    ["Type"] = "Potion",
    ["Name"] = "Lucky",
    ["Level"] = 4,
    ["Amount"] = 5
}):Add(10, {
    ["Type"] = "Potion",
    ["Name"] = "Speed",
    ["Level"] = 4,
    ["Amount"] = 5
}):Add(10, {
    ["Type"] = "Potion",
    ["Name"] = "Coins",
    ["Level"] = 4,
    ["Amount"] = 5
}):Add(10, {
    ["Type"] = "Potion",
    ["Name"] = "Mythic",
    ["Level"] = 4,
    ["Amount"] = 5
}):Add(6, {
    ["Type"] = "Potion",
    ["Name"] = "Lucky",
    ["Level"] = 5,
    ["Amount"] = 1
}):Add(6, {
    ["Type"] = "Potion",
    ["Name"] = "Speed",
    ["Level"] = 5,
    ["Amount"] = 1
}):Add(6, {
    ["Type"] = "Potion",
    ["Name"] = "Coins",
    ["Level"] = 5,
    ["Amount"] = 1
}):Add(6, {
    ["Type"] = "Potion",
    ["Name"] = "Mythic",
    ["Level"] = 5,
    ["Amount"] = 1
}):Add(4, {
    ["Type"] = "Potion",
    ["Name"] = "Lucky",
    ["Level"] = 6,
    ["Amount"] = 1
}):Add(4, {
    ["Type"] = "Potion",
    ["Name"] = "Speed",
    ["Level"] = 6,
    ["Amount"] = 1
}):Add(4, {
    ["Type"] = "Potion",
    ["Name"] = "Coins",
    ["Level"] = 6,
    ["Amount"] = 1
}):Add(4, {
    ["Type"] = "Potion",
    ["Name"] = "Mythic",
    ["Level"] = 6,
    ["Amount"] = 1
}):Add(2, {
    ["Type"] = "Potion",
    ["Name"] = "Infinity Elixir",
    ["Amount"] = 1
}):Build()
local u12 = {}
local v13 = {
    ["Type"] = "Collect",
    ["Item"] = {
        ["Type"] = "Currency",
        ["Currency"] = "Coins",
        ["Amount"] = 5000000
    }
}
local v14 = {
    ["Type"] = "Collect",
    ["Item"] = {
        ["Type"] = "Currency",
        ["Currency"] = "Coins",
        ["Amount"] = 10000000
    }
}
__set_list(u12, 1, {{
    ["Type"] = "Hatch",
    ["Amount"] = 50
}, {
    ["Type"] = "Hatch",
    ["Amount"] = 65
}, {
    ["Type"] = "Bubbles",
    ["Amount"] = 500000
}, {
    ["Type"] = "Bubbles",
    ["Amount"] = 650000
}, v13, v14, {
    ["Type"] = "Hatch",
    ["Rarity"] = "Common",
    ["Amount"] = 40
}, {
    ["Type"] = "Hatch",
    ["Rarity"] = "Rare",
    ["Amount"] = 30
}, {
    ["Type"] = "Hatch",
    ["Rarity"] = "Epic",
    ["Amount"] = 10
}})
return function(p15, p16) --[[Function name: GenieQuest, line 73]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u9
        [3] = u10
        [4] = u5
        [5] = u12
        [6] = u4
        [7] = u7
        [8] = u8
        [9] = u11
        [10] = u6
    --]]
    local v17 = Random.new(p16)
    local v18 = u2(u9, v17)
    local v19 = u2(u10, v17)
    local v20 = u5.new("gem-genie"):Visual("Gem Genie", true)
    local v21 = {}
    local v22 = {
        ["Type"] = "Currency",
        ["Currency"] = "Gems"
    }
    local v23 = v17:NextNumber(3500, 5000) / 250
    v22.Amount = math.round(v23) * 250
    __set_list(v21, 1, {v22})
    if (p15.QuestsCompleted["gem-genie"] or 0) < -1 then
        local v24 = {}
        while true do
            local v25 = u12[v17:NextInteger(1, #u12)]
            local v26 = false
            for _, v27 in v24 do
                if u4:CompareTask(v27, v25) then
                    v26 = true
                    break
                end
            end
            if not v26 then
                table.insert(v24, v25)
            end
            local v28 = #v24
            local v29 = v19 - 1
            if math.max(1, v29) <= v28 then
                for _, v30 in v24 do
                    v20:Task(v30)
                end
                local v31 = v17:NextInteger(-2, 3)
                if v31 > 0 then
                    table.insert(v21, {
                        ["Type"] = "Powerup",
                        ["Name"] = "Mystery Box",
                        ["Amount"] = v31
                    })
                end
                v18 = v18 - 1
                local v32 = {
                    ["Type"] = "Currency",
                    ["Currency"] = "Coins"
                }
                local v33 = v17:NextNumber(3500, 5000) / 250
                v32.Amount = math.round(v33) * 250
                table.insert(v21, v32)
                ::l18::
                if v18 > 0 then
                    for _ = 1, v18 do
                        local v34 = u2
                        local v35 = u11
                        table.insert(v21, v34(v35, v17))
                    end
                end
                for _, v36 in u6:Combine(v21) do
                    v20:Reward(v36)
                end
                return v20:Build()
            end
        end
    else
        for _, v37 in u7:GetRandomChallenges(p15, v19, u8.Hourly, v17) do
            if v37.Task.Amount then
                local v38 = table.clone(v37.Task)
                local v39 = v37.Task.Type == "Hatch" and v37.Task.Egg and 2 or 3
                local v40 = v38.Amount * v39
                v38.Amount = math.ceil(v40)
                v20:Task(v38)
            else
                v20:Task(v37.Task)
            end
        end
        goto l18
    end
end
