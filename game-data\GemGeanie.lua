local v1 = game:GetService("Players")
local v2 = game:GetService("ReplicatedStorage")
local u3 = game:GetService("HttpService")
local u4 = game:GetService("TweenService")
local u5 = game:GetService("MarketplaceService")
require(v2.Client.Gui.Utils.UpdateWhileGuiOpen)
local u6 = require(v2.Client.Gui.GuiFrame)
local u7 = require(v2.Client.Framework.Services.LocalData)
local u8 = require(v2.Shared.Utils.Stats.QuestUtil)
local u9 = require(v2.Shared.Data.Quests.GenieQuest)
local v10 = require(v2.Shared.Framework.Classes.Pool)
local u11 = require(v2.Client.Gui.Tooltip)
local u12 = require(v2.Shared.Data.Builders.TooltipBuilder)
require(v2.Shared.Types)
local u13 = require(v2.Shared.Utils.Stats.ItemUtil)
local u14 = require(v2.Shared.Framework.Utilities.String.FormatSuffix)
local v15 = require(v2.Shared.Framework.Classes.Emitter)
local u16 = require(v2.Client.Framework.Services.InputMode)
local u17 = require(v2.Client.Gui.Utils.ClickableButton)
local u18 = require(v2.Shared.Framework.Network.Remote)
local u19 = require(v2.Shared.Framework.Utilities.every)
local u20 = require(v2.Client.Effects.OutlinePulse)
local u21 = require(v2.Shared.Framework.Utilities.Math.Time)
local u22 = require(v2.Shared.Utils.RichText)
local u23 = require(v2.Shared.Utils.AnimateColor)
local v24 = require(v2.Client.Activation)
local u25 = require(v2.Client.Gui.Utils.Notification)
local u26 = require(v2.Client.Gui.Utils.AutoButtonColor)
local u27 = require(v2.Shared.Palette)
local u28 = require(v2.Shared.Data.Builders.PromptBuilder)
local u29 = require(v2.Client.Gui.Prompt)
local u30 = require(v2.Client.Gui.Utils.PlayLocalSound)
local u31 = require(v2.Shared.Utils.Stats.StatsUtil)
local u32 = require(v2.Client.Gui.Utils.Achievement)
local u33 = require(v2.Client.Gui.Utils.Shiny)
local u34 = require(v2.Client.Gui.Animations.ProcessingPurchase)
local u35 = require(v2.Shared.Constants)
local v36 = require(v2.Shared.Utils.Stats.ItemUtil.OrbIcon)
local u37 = require(v2.Client.Framework.Utilities.Gui.Animations.SetFade)
local u38 = v1.LocalPlayer
local u39 = u38.PlayerGui.ScreenGui
local u40 = u39.GemGenie
local u41 = u40.Skip
local u42 = u40.Cards.Template
u42.Beam.BackgroundTransparency = 1
u42.Inner.Choose.Cover.BackgroundTransparency = 1
u42.Inner.Cover.BackgroundTransparency = 1
u42.Parent = nil
local u43 = u42.Inner.Content.Tasks.Template
u43.Parent = nil
local u44 = u42.Inner.Content.Rewards.Template
u44.Parent = nil
local u45 = workspace.Worlds["The Overworld"].Islands.Zen.Island.GemGenie
local u46 = v15.fromEmitter(v2.Assets.Particles.Stars)
u46.Parent = nil
u46.Rate = 0
local u47 = u22.new():size(20)
local u48 = TweenInfo.new(0.4)
TweenInfo.new(0.25, Enum.EasingStyle.Sine)
TweenInfo.new(0.75, Enum.EasingStyle.Back)
local u49 = u39.WorldMap.Worlds["The Overworld"].Islands.Zen
local u50 = {}
local u51 = nil
local v52 = nil
local u53 = u39.GemGenieTask
local u54 = u53.Content.Info.Bar.Inner
local u58 = v10.new(function() --[[Anonymous function at line 82]]
    --[[
    Upvalues:
        [1] = u44
        [2] = u11
        [3] = u3
        [4] = u12
    --]]
    local u55 = u44:Clone()
    u11:OnEnter(u55.Hover, function() --[[Anonymous function at line 84]]
        --[[
        Upvalues:
            [1] = u55
            [2] = u3
            [3] = u12
        --]]
        local v56 = u55:GetAttribute("Item")
        if v56 and typeof(v56) == "string" then
            local v57 = u3:JSONDecode(v56)
            if v57 then
                return u12.fromItem(v57)
            end
        end
    end)
    return u55
end)
local u59 = v10.new(function() --[[Anonymous function at line 99]]
    --[[
    Upvalues:
        [1] = u43
    --]]
    return u43:Clone()
end)
local u60 = 1
local u61 = u53.Content.Next
local u62 = nil
local function u71() --[[Anonymous function at line 108]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u8
        [3] = u60
        [4] = u62
    --]]
    local v63 = u7:Get()
    if v63 then
        local v64 = u8:FindById(v63, "gem-genie")
        if v64 then
            local v65 = {}
            local v66 = #v64.Tasks
            if v66 < u60 then
                u60 = 1
            end
            for v67 = 0, v66 - 1 do
                local v68 = (u60 - 1 + v67) % v66 + 1
                local v69 = {
                    ["Index"] = v68,
                    ["Task"] = v64.Tasks[v68]
                }
                table.insert(v65, v69)
            end
            for _, v70 in v65 do
                if u8:GetRequirement(v70.Task) > (v64.Progress[v70.Index] or 0) then
                    u60 = v70.Index
                    u62 = v70.Task
                    return
                end
            end
        end
    else
        return
    end
end
local function u83() --[[Anonymous function at line 147]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u8
        [3] = u71
        [4] = u60
        [5] = u53
        [6] = u54
        [7] = u14
        [8] = u61
        [9] = u62
        [10] = u39
        [11] = u32
    --]]
    local v72 = u7:Get()
    if v72 then
        local v73 = u8:FindById(v72, "gem-genie")
        if v73 then
            u71()
            local v74 = v73.Tasks[u60]
            if v74 then
                local v75 = v73.Progress[u60] or 0
                local v76 = (v74.Type ~= "Hatch" or not v74.Rarity) and true or false
                u53.Content.Icon.Label.Visible = v76
                local v77 = u8:GetRequirement(v74)
                u53.Content.Info.Task.Text = u8:FormatTask(v74)
                u54.Fill.Size = UDim2.new(v75 / v77, 0, 1, 6)
                u54.Fill.Visible = v75 > 0
                u54.Label.Text = ("%* / %*"):format(u14(v75, 10000), (u14(v77, 10000)))
                u8:UpdateTaskIcon(u53.Content.Icon, v74)
                u53.Content.Icon.Label.Size = UDim2.fromOffset(50, 50)
            end
            local v78 = 0
            for v79, v80 in v73.Tasks do
                if (v73.Progress[v79] or 0) < u8:GetRequirement(v80) then
                    v78 = v78 + 1
                end
            end
            u61.Visible = v78 > 1
        else
            u60 = 1
            u62 = nil
        end
        local _ = v73 == nil
        local _ = u39.HUD.Tutorial.Visible or u32.Active
        local v81 = u53
        local v82
        if v73 == nil then
            v82 = false
        else
            v82 = u39.IslandRace.Visible == false
        end
        v81.Visible = v82
    end
end
u39.IslandRace:GetPropertyChangedSignal("Visible"):Connect(u83)
u26(u61.Button, u27.Button.Default)
u17(u61.Button, function() --[[Anonymous function at line 203]]
    --[[
    Upvalues:
        [1] = u60
        [2] = u83
    --]]
    u60 = u60 + 1
    u83()
end)
local u84 = nil
local function u90(p85) --[[Anonymous function at line 210]]
    --[[
    Upvalues:
        [1] = u50
        [2] = u46
        [3] = u4
        [4] = u48
        [5] = u84
    --]]
    for v86, v87 in u50 do
        local v88 = v86 == p85
        if v88 then
            u46.Rate = 10
            u46.Parent = v87.Beam.Emitter
        end
        local v89 = p85 and not v88 and 0.25 or 1
        u4:Create(v87.Beam, u48, {
            ["BackgroundTransparency"] = v88 and 0.65 or 1
        }):Play()
        u4:Create(v87.Inner.Cover, u48, {
            ["BackgroundTransparency"] = v89
        }):Play()
        u4:Create(v87.Inner.Choose.Cover, u48, {
            ["BackgroundTransparency"] = v89
        }):Play()
        v87.ZIndex = v88 and 3 or 1
    end
    if not p85 then
        u46.Parent = nil
        u46.Rate = 0
    end
    u84 = p85
end
local u91 = nil
local function u126() --[[Anonymous function at line 256]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u21
        [3] = u22
        [4] = u47
        [5] = u8
        [6] = u91
        [7] = u6
        [8] = u25
        [9] = u45
        [10] = u49
        [11] = u40
        [12] = u50
        [13] = u9
        [14] = u51
        [15] = u23
        [16] = u58
        [17] = u31
        [18] = u13
        [19] = u3
        [20] = u59
        [21] = u41
        [22] = u42
    --]]
    local v92 = u7:Get()
    if v92 then
        local v93 = u21.now()
        local v94 = v92.GemGenie.Next - v93
        local v95 = v94 > 0 and u22.autoStroke((("%*\n%*"):format(u47("Come Back In"), (u21.formatTime(v94))))) or u22.autoStroke((("%*\nLOCKED"):format((u47("Finish Current Quest")))))
        local v96 = u8:FindById(v92, "gem-genie")
        local v97 = v94 > 0 and true or v96 ~= nil
        if u91 ~= v97 then
            if u91 ~= nil and not (v97 or u6:IsAnyOpen()) then
                u25(("The %* awaits your return..."):format((u22.autoColor("Gem Genie", Color3.fromRGB(188, 71, 255)))), {
                    ["Sound"] = "NotificationRift",
                    ["TeleportId"] = u45.Root:GetFullName()
                })
            end
            u91 = v97
        end
        local v98 = u49.Timer
        local v99
        if v94 > 0 then
            v99 = u49.Locked.Visible == false
        else
            v99 = false
        end
        v98.Visible = v99
        u49.Timer.Text = u21.formatRealTime(v94)
        u49.Button.Icon.Label.ImageTransparency = v94 > 0 and 0.5 or 0
        local v100 = not v97 and "Choose your destiny!" or (v94 <= 0 and "Complete your current quest!" or ("Come back in %*"):format((u21.formatTime(v94))))
        u45.Root.BillboardGui.Description.Text = v100
        if u6:IsOpen("GemGenie") then
            local v101 = v92.GemGenie.Seed
            u40.Title.Text = v97 and "Active Quest" or "CHOOSE YOUR DESTINY!"
            local v102 = u40.Reroll
            local v103 = not v97
            if v103 then
                v103 = v94 <= 0
            end
            v102.Visible = v103
            local v104 = false
            for v105 = 1, 3 do
                local v106 = u50[v105]
                local v107 = u9(v92, v101 + (v105 - 1))
                local v108
                if v96 then
                    v108 = u8:Compare(v96, v107) or false
                else
                    v108 = false
                end
                v106.Inner.Choose.Visible = not v97
                v104 = v108 and true or v104
                if v105 == 3 and (not v104 and v96) then
                    v107 = v96
                    v108 = true
                end
                if v97 then
                    v106.Inner.Content.Visible = v108
                    v106.Inner.Choose.Visible = false
                    local v109 = v106.Inner.Change
                    local v110
                    if v108 then
                        v110 = v94 <= 0
                    else
                        v110 = v108
                    end
                    v109.Visible = v110
                    v106.Inner.Info.Visible = not v108
                else
                    v106.Visible = true
                    v106.Inner.Info.Visible = false
                    v106.Inner.Content.Visible = true
                    v106.Inner.Choose.Visible = true
                    v106.Inner.Change.Visible = false
                end
                if v108 then
                    u51 = v105
                    u23.Shift(v106.Inner.Border, "Rainbow", {
                        ["Speed"] = 0.25
                    })
                else
                    u23.None(v106.Inner.Border)
                    v106.Inner.Border.ImageColor3 = Color3.fromRGB(42, 60, 70)
                end
                for v111, v112 in v107.Rewards do
                    local v113 = u58:Get()
                    local v114
                    if v112.Type == "Currency" then
                        v114 = table.clone(v112)
                        local v115 = v112.Amount * u31:GetCurrencyMultiplier(v92, v112.Currency, true)
                        v114.Amount = math.floor(v115)
                    else
                        v114 = v112
                    end
                    u13:FormatAmount(v113.Amount, v114)
                    v113.LayoutOrder = v111
                    v113.Parent = v106.Inner.Content.Rewards
                    v113:SetAttribute("Item", u3:JSONEncode(v114))
                    u13:UpdateIcon(v113, v114)
                end
                for v116, v117 in v107.Tasks do
                    local v118 = u59:Get()
                    v118.LayoutOrder = v116
                    v118.Label.Text = u8:FormatTask(v117)
                    v118.Parent = v106.Inner.Content.Tasks
                    v118.Size = UDim2.new(1, 0, 0, v108 and 62 or 50)
                    local v119 = v96 and v108 and v96.Tasks[v116]
                    if v119 then
                        local v120 = v96.Progress[v116] / u8:GetRequirement(v119)
                        local v121 = math.min(1, v120)
                        local v122 = v118.Bar.Label
                        local v123 = v121 * 100
                        v122.Text = ("%*%%"):format((math.ceil(v123)))
                        v118.Bar.Fill.Size = UDim2.new(v121, 0, 0, 8)
                    end
                    v118.Bar.Visible = v108
                    u8:UpdateTaskIcon(v118.Icon, v117)
                end
            end
            local v124 = 0
            for _, v125 in u50 do
                if v125.Inner.Info.Visible then
                    v124 = v124 + 1
                    v125.Inner.Info.Text = v95
                end
            end
            u41.Visible = v124 >= 3
            if not v96 then
                u51 = nil
            end
            u40.Title.Position = UDim2.new(0.5, u51 and (u42.Size.X.Offset + 30) * (u51 - 2) or 0, 0.5, -305)
            u58:Done()
            u59:Done()
        end
    else
        return
    end
end
local u127 = v52
local u128 = u51
local u129 = u84
for u130 = 1, 3 do
    local u131 = u42:Clone()
    local v132 = (u42.Size.X.Offset / 2 + 30) * (u130 - 2)
    local v133 = UDim2.new(0.5, v132, 0.5, 0)
    u131.AnchorPoint = Vector2.new(1 - (u130 - 1) / 2, 0.5)
    u131.Position = v133
    u131.Parent = u40
    u131.Hover.MouseEnter:Connect(function() --[[Anonymous function at line 429]]
        --[[
        Upvalues:
            [1] = u16
            [2] = u128
            [3] = u90
            [4] = u130
        --]]
        if u16.Mode ~= "Touch" and not u128 then
            u90(u130)
        end
    end)
    u131.Hover.MouseLeave:Connect(function() --[[Anonymous function at line 435]]
        --[[
        Upvalues:
            [1] = u129
            [2] = u130
            [3] = u90
        --]]
        if u129 == u130 then
            u90(nil)
        end
    end)
    u131.Hover.Activated:Connect(function() --[[Function name: activate, line 419]]
        --[[
        Upvalues:
            [1] = u128
            [2] = u30
            [3] = u18
            [4] = u130
            [5] = u131
            [6] = u20
            [7] = u127
            [8] = u19
            [9] = u50
            [10] = u90
        --]]
        if not u128 then
            u30("QuestStart")
            u18:FireServer("StartGenieQuest", u130)
            local u134 = u131
            u20(function(p135) --[[Anonymous function at line 231]]
                --[[
                Upvalues:
                    [1] = u134
                --]]
                p135.UICorner.CornerRadius = u134.Inner.UICorner.CornerRadius
                p135.UIStroke.Thickness = 5
            end).Parent = u134.Inner
            if u127 then
                u127:Disconnect()
            end
            u127 = u19(1, function() --[[Anonymous function at line 243]]
                --[[
                Upvalues:
                    [1] = u128
                    [2] = u50
                    [3] = u20
                --]]
                if u128 then
                    local u136 = u50[u128]
                    if u136 then
                        u20(function(p137) --[[Anonymous function at line 231]]
                            --[[
                            Upvalues:
                                [1] = u136
                            --]]
                            p137.UICorner.CornerRadius = u136.Inner.UICorner.CornerRadius
                            p137.UIStroke.Thickness = 5
                        end).Parent = u136.Inner
                    end
                end
            end)
            u90(nil)
        end
    end)
    u50[u130] = u131
    u26(u131.Inner.Choose.Button, u27.Button.Default)
    u17(u131.Inner.Choose.Button, function() --[[Anonymous function at line 445]]
        --[[
        Upvalues:
            [1] = u128
            [2] = u30
            [3] = u18
            [4] = u130
            [5] = u131
            [6] = u20
            [7] = u127
            [8] = u19
            [9] = u50
            [10] = u90
        --]]
        if not u128 then
            u30("QuestStart")
            u18:FireServer("StartGenieQuest", u130)
            local u138 = u131
            u20(function(p139) --[[Anonymous function at line 231]]
                --[[
                Upvalues:
                    [1] = u138
                --]]
                p139.UICorner.CornerRadius = u138.Inner.UICorner.CornerRadius
                p139.UIStroke.Thickness = 5
            end).Parent = u138.Inner
            if u127 then
                u127:Disconnect()
            end
            u127 = u19(1, function() --[[Anonymous function at line 243]]
                --[[
                Upvalues:
                    [1] = u128
                    [2] = u50
                    [3] = u20
                --]]
                if u128 then
                    local u140 = u50[u128]
                    if u140 then
                        u20(function(p141) --[[Anonymous function at line 231]]
                            --[[
                            Upvalues:
                                [1] = u140
                            --]]
                            p141.UICorner.CornerRadius = u140.Inner.UICorner.CornerRadius
                            p141.UIStroke.Thickness = 5
                        end).Parent = u140.Inner
                    end
                end
            end)
            u90(nil)
        end
    end, function() --[[Anonymous function at line 447]]
        --[[
        Upvalues:
            [1] = u131
        --]]
        return u131.Inner.Choose.Cover.BackgroundTransparency == 1
    end)
    u26(u131.Inner.Change.Button, u27.Button.Red)
    u17(u131.Inner.Change.Button, function() --[[Anonymous function at line 452]]
        --[[
        Upvalues:
            [1] = u28
            [2] = u22
            [3] = u27
            [4] = u26
            [5] = u17
            [6] = u18
            [7] = u29
        --]]
        u29(u28.new("Change?"):Text((("%*\nThis cannot be undone."):format((u22.autoColor("You\'re about to remove your current quest!", u27.Text.Pink))))):Button(function(p142, u143) --[[Anonymous function at line 455]]
            --[[
            Upvalues:
                [1] = u26
                [2] = u27
                [3] = u17
                [4] = u18
            --]]
            p142.Label.Text = "Change"
            u26(p142, u27.Button.Red)
            u17(p142, function() --[[Anonymous function at line 458]]
                --[[
                Upvalues:
                    [1] = u18
                    [2] = u143
                --]]
                u18:FireServer("ChangeGenieQuest")
                u143()
            end)
        end):Button(function(p144, p145) --[[Anonymous function at line 463]]
            --[[
            Upvalues:
                [1] = u26
                [2] = u27
                [3] = u17
            --]]
            p144.Label.Text = "Cancel"
            u26(p144, u27.Button.Inactive)
            u17(p144, p145)
        end):Build())
    end)
end
local u146 = u35.GemGenieReroll
local u147 = u40.Reroll
u147.Button.Container.Label.Text = u13:GetAmount(u146)
v36.new(u147.Button.Container.Frame, u146)
local u148 = u22.new():size(24)
u26(u147.Button, u27.Button.Pink)
u17(u147.Button, function() --[[Anonymous function at line 486]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u13
        [3] = u146
        [4] = u28
        [5] = u29
        [6] = u30
        [7] = u18
    --]]
    local v149 = u7:Get()
    if v149 then
        if u13:GetOwnedAmount(v149, u146) < u13:GetAmount(u146) then
            return u29(u28.fromNotEnough(v149, u146, function(p150) --[[Anonymous function at line 492]]
                return ("You need %* to reroll these quests!"):format(p150)
            end):Build())
        end
        u30("Reroll")
        u18:FireServer("RerollGenie")
    end
end)
u7:ConnectDataChanged("Powerups", function(p151) --[[Anonymous function at line 501]]
    --[[
    Upvalues:
        [1] = u13
        [2] = u146
        [3] = u147
        [4] = u14
        [5] = u148
        [6] = u37
    --]]
    local v152 = u13:GetOwnedAmount(p151, u146)
    local v153 = u13
    local v154 = u146
    u147.Button.Container.Label.Text = u14(v152, 1000) .. u148((("/%*"):format((tostring(v153:GetAmount(v154))))))
    u37(u147, u13:GetAmount(u146) <= v152 and 0 or 0.5, nil, { "Frame" })
end)
u147.Button.Container.Label.Size = UDim2.new(0, u147.Button.Container.Label.TextBounds.X / u39.UIScale.Scale, 1, 0)
u147.Button.Container.Label:GetPropertyChangedSignal("TextBounds"):Connect(function() --[[Function name: update, line 481]]
    --[[
    Upvalues:
        [1] = u147
        [2] = u39
    --]]
    u147.Button.Container.Label.Size = UDim2.new(0, u147.Button.Container.Label.TextBounds.X / u39.UIScale.Scale, 1, 0)
end)
u26(u40.Close, u27.Button.Red)
u17(u40.Close, function() --[[Anonymous function at line 514]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    u6:Close()
end)
u26(u41.Button, u27.Button.Orange)
u17(u41.Button, function() --[[Anonymous function at line 520]]
    --[[
    Upvalues:
        [1] = u34
        [2] = u5
        [3] = u38
        [4] = u35
    --]]
    u34:Show()
    u5:PromptProductPurchase(u38, u35.GemGenieSkipProduct)
end)
u32.Created:Connect(u83)
u32.Destroyed:Connect(u83)
u7:ConnectDataChanged({ "Quests", "QuestsCompleted" }, u83)
u39.HUD.Tutorial:GetPropertyChangedSignal("Visible"):Connect(u83)
u6:BindOnOpened("GemGenie", function() --[[Anonymous function at line 531]]
    --[[
    Upvalues:
        [1] = u126
        [2] = u127
        [3] = u19
        [4] = u128
        [5] = u50
        [6] = u20
        [7] = u33
        [8] = u41
    --]]
    u126()
    if u127 then
        u127:Disconnect()
    end
    u127 = u19(1, function() --[[Anonymous function at line 243]]
        --[[
        Upvalues:
            [1] = u128
            [2] = u50
            [3] = u20
        --]]
        if u128 then
            local u155 = u50[u128]
            if u155 then
                u20(function(p156) --[[Anonymous function at line 231]]
                    --[[
                    Upvalues:
                        [1] = u155
                    --]]
                    p156.UICorner.CornerRadius = u155.Inner.UICorner.CornerRadius
                    p156.UIStroke.Thickness = 5
                end).Parent = u155.Inner
            end
        end
    end)
    u33:Add(u41.Effect)
    game.StarterGui:SetCoreGuiEnabled(Enum.CoreGuiType.PlayerList, false)
end)
u6:BindOnClosed("GemGenie", function() --[[Anonymous function at line 537]]
    --[[
    Upvalues:
        [1] = u127
        [2] = u33
        [3] = u41
    --]]
    if u127 then
        u127:Disconnect()
        u127 = nil
    end
    u33:Remove(u41.Effect)
    game.StarterGui:SetCoreGuiEnabled(Enum.CoreGuiType.PlayerList, true)
end)
u19(0.5, u126)
u7:ConnectDataChanged({ "Quests", "GemGenie" }, u126)
v24:Bind(u45, function() --[[Anonymous function at line 549]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    u6:Open("GemGenie")
end)
return {}
