local v1 = game:GetService("ReplicatedStorage")
local v2 = game:GetService("CollectionService")
local v3 = require(v1.Shared.Data.Builders.EggBuilder)
require(v1.Shared.Data.Currency)
local v4 = require(v1.Shared.Data.Pets)
local v5 = require(v1.Shared.Constants)
local v6 = require(v1.Shared.Data.Powerups)
local v7 = {
    ["Inferno Egg"] = v3.new():Product(3222726170):Image("rbxassetid://99740547547936"):Pet(100, "Inferno Dragon"):Build(),
    ["Season 1 Egg"] = v3.new():Image("rbxassetid://120126304713239"):<PERSON>(90, "Enlightened Kitty"):Pet(10, "Judgement"):<PERSON>(0.5, "Ophani<PERSON>"):Pet(0.01, "Seraph"):Build(),
    ["Series 1 Egg"] = v3.new():Image("rbxassetid://75203581016320"):<PERSON>(90, "Competitor Doggy"):<PERSON>(9.9, "Golden Golem"):Pet(0.1, "Parasite"):<PERSON>(0.01, "Starlight"):<PERSON>(0.001, "Overseer"):Build(),
    ["Aura Egg"] = v3.new():Cost({
        ["Type"] = "Currency",
        ["Currency"] = "Coins",
        ["Amount"] = 7777777
    }):Pet(65, "Manny"):Pet(30, "Manicorn"):Pet(0.01, "Sigma Serpent"):Pet(0.005, "Manarium"):Pet(0.0001, "MAN FACE GOD"):Build(),
    ["Silly Egg"] = v3.new():Cost({
        ["Type"] = "Currency",
        ["Currency"] = "Coins",
        ["Amount"] = 10000000
    }):Pet(65, "Bunnnny"):Pet(30, "Long Kitty"):Pet(0.01, "DOOF"):Pet(0.0004, "ROUND"):Pet(0.0001, "Silly Doggy :)"):Build(),
    ["Common Egg"] = v3.new():Image("rbxassetid://99740547547936"):World("The Overworld"):Cost({
        ["Type"] = "Currency",
        ["Currency"] = "Coins",
        ["Amount"] = 10
    }):Reward({
        ["Type"] = "Currency",
        ["Currency"] = "Coins",
        ["Amount"] = 500
    }, {
        ["Type"] = "Currency",
        ["Currency"] = "Coins",
        ["Amount"] = 3000
    }):Pet(40, "Doggy"):Pet(30, "Kitty"):Pet(25, "Bunny"):Pet(5, "Bear"):Pet(1e-6, "King Doggy"):Build(),
    ["Spotted Egg"] = v3.new():Image("rbxassetid://93413159913237"):World("The Overworld"):Cost({
        ["Type"] = "Currency",
        ["Currency"] = "Coins",
        ["Amount"] = 110
    }):Reward({
        ["Type"] = "Currency",
        ["Currency"] = "Coins",
        ["Amount"] = 2500
    }, {
        ["Type"] = "Powerup",
        ["Name"] = "Spin Ticket",
        ["Amount"] = 5
    }):Pet(40, "Mouse"):Pet(25, "Wolf"):Pet(20, "Fox"):Pet(10, "Polar Bear"):Pet(5, "Panda"):Build(),
    ["Iceshard Egg"] = v3.new():Image("rbxassetid://97260247088392"):World("The Overworld"):Cost({
        ["Type"] = "Currency",
        ["Currency"] = "Coins",
        ["Amount"] = 450
    }):Reward({
        ["Type"] = "Currency",
        ["Currency"] = "Coins",
        ["Amount"] = 3500
    }, {
        ["Type"] = "Powerup",
        ["Name"] = "Power Orb",
        ["Amount"] = 1
    }):Pet(30, "Ice Kitty"):Pet(25, "Deer"):Pet(20, "Ice Wolf"):Pet(14, "Piggy"):Pet(8, "Ice Deer"):Pet(3, "Ice Dragon"):Build(),
    ["Spikey Egg"] = v3.new():Image("rbxassetid://139122913537518"):World("The Overworld"):Cost({
        ["Type"] = "Currency",
        ["Currency"] = "Coins",
        ["Amount"] = 5000
    }):Reward({
        ["Type"] = "Potion",
        ["Name"] = "Coins",
        ["Level"] = 2,
        ["Amount"] = 2
    }, {
        ["Type"] = "Potion",
        ["Name"] = "Speed",
        ["Level"] = 5,
        ["Amount"] = 2
    }):Pet(30, "Golem"):Pet(30, "Dinosaur"):Pet(20, "Ruby Golem"):Pet(12, "Dragon"):Pet(6.5, "Dark Dragon"):Pet(0.5, "Emerald Golem"):Build(),
    ["Magma Egg"] = v3.new():Image("rbxassetid://70636967986690"):World("The Overworld"):Cost({
        ["Type"] = "Currency",
        ["Currency"] = "Coins",
        ["Amount"] = 15000
    }):Reward({
        ["Type"] = "Potion",
        ["Name"] = "Coins",
        ["Level"] = 4,
        ["Amount"] = 2
    }, {
        ["Type"] = "Powerup",
        ["Name"] = "Royal Key",
        ["Amount"] = 1
    }):Pet(45, "Magma Doggy"):Pet(30.5, "Magma Deer"):Pet(15, "Magma Fox"):Pet(7, "Magma Bear"):Pet(2.25, "Demon"):Pet(0.25, "Inferno Dragon"):Build(),
    ["Crystal Egg"] = v3.new():Image("rbxassetid://140343951272706"):World("The Overworld"):Cost({
        ["Type"] = "Currency",
        ["Currency"] = "Coins",
        ["Amount"] = 75000
    }):Reward({
        ["Type"] = "Potion",
        ["Name"] = "Speed",
        ["Level"] = 4,
        ["Amount"] = 2
    }, {
        ["Type"] = "Potion",
        ["Name"] = "Mythic",
        ["Level"] = 5,
        ["Amount"] = 5
    }):Pet(60, "Cave Bat"):Pet(29.9, "Dark Bat"):Pet(7, "Angel"):Pet(2.25, "Emerald Bat"):Pet(0.25, "Unicorn"):Pet(0.1, "Flying Pig"):Build(),
    ["Lunar Egg"] = v3.new():Image("rbxassetid://83785616369556"):World("The Overworld"):Cost({
        ["Type"] = "Currency",
        ["Currency"] = "Coins",
        ["Amount"] = 100000
    }):Reward({
        ["Type"] = "Currency",
        ["Currency"] = "Gems",
        ["Amount"] = 4500
    }, {
        ["Type"] = "Powerup",
        ["Name"] = "Power Orb",
        ["Amount"] = 3
    }):Pet(60, "Space Mouse"):Pet(29.9, "Space Bull"):Pet(7, "Lunar Fox"):Pet(2.25, "Lunarcorn"):Pet(0.25, "Lunar Serpent"):Pet(0.1, "Electra"):Build(),
    ["Void Egg"] = v3.new():Image("rbxassetid://115778916864618"):World("The Overworld"):Cost({
        ["Type"] = "Currency",
        ["Currency"] = "Coins",
        ["Amount"] = 175000
    }):Reward({
        ["Type"] = "Powerup",
        ["Name"] = "Golden Key",
        ["Amount"] = 25
    }, {
        ["Type"] = "Powerup",
        ["Name"] = "Royal Key",
        ["Amount"] = 5
    }):Pet(65, "Void Kitty"):Pet(31.9, "Void Bat"):Pet(2.25, "Void Demon"):Pet(0.05, "Dark Phoenix"):Pet(0.02, "Neon Elemental"):Pet(0.0001, "NULLVoid"):Build(),
    ["Hell Egg"] = v3.new():Image("rbxassetid://83138810469675"):World("The Overworld"):Cost({
        ["Type"] = "Currency",
        ["Currency"] = "Coins",
        ["Amount"] = 300000
    }):Reward({
        ["Type"] = "Currency",
        ["Currency"] = "Gems",
        ["Amount"] = 10000
    }, {
        ["Type"] = "Potion",
        ["Name"] = "Speed",
        ["Level"] = 6,
        ["Amount"] = 1
    }):Pet(60, "Hell Piggy"):Pet(29.9, "Hell Dragon"):Pet(7, "Hell Crawler"):Pet(2.25, "Inferno Demon"):Pet(0.025, "Inferno Cube"):Pet(0.002, "Virus"):Build(),
    ["Nightmare Egg"] = v3.new():Image("rbxassetid://78128449482196"):World("The Overworld"):Cost({
        ["Type"] = "Currency",
        ["Currency"] = "Coins",
        ["Amount"] = 900000
    }):Reward({
        ["Type"] = "Potion",
        ["Name"] = "Lucky",
        ["Level"] = 6,
        ["Amount"] = 2
    }, {
        ["Type"] = "Potion",
        ["Name"] = "Infinity Elixir",
        ["Amount"] = 1
    }):Pet(60, "Demon Doggy"):Pet(29.9, "Skeletal Deer"):Pet(7, "Night Crawler"):Pet(2.25, "Hell Bat"):Pet(0.004, "Green Hydra"):Pet(0.001, "Demonic Hydra"):Pet(2e-6, "The Overlord"):Build(),
    ["Rainbow Egg"] = v3.new():Image("rbxassetid://101322128600967"):World("The Overworld"):Cost({
        ["Type"] = "Currency",
        ["Currency"] = "Coins",
        ["Amount"] = 1500000
    }):Reward({
        ["Type"] = "Potion",
        ["Name"] = "Infinity Elixir",
        ["Amount"] = 1
    }, {
        ["Type"] = "Potion",
        ["Name"] = "Lucky",
        ["Level"] = 6,
        ["Amount"] = 2
    }):Pet(60, "Red Golem"):Pet(29.9, "Orange Deer"):Pet(7, "Yellow Fox"):Pet(2.25, "Green Angel"):Pet(0.001, "Hexarium"):Pet(0.0005, "Rainbow Shock"):Build(),
    ["Pastel Egg"] = v3.new():Event("easter-2025"):Image("rbxassetid://72274303112126"):Cost({
        ["Type"] = "Currency",
        ["Currency"] = "Coins",
        ["Amount"] = 800000
    }):Pet(60, "Paper Doggy"):Pet(29.9, "Paper Bunny"):Pet(7, "Chubby Bunny"):Pet(2.25, "Hatchling"):Pet(0.004, "Sweet Treat"):Pet(0.0008, "Rainbow Marshmellow"):Pet(2e-6, "Giant Chocolate Chicken"):Build(),
    ["Bunny Egg"] = v3.new():Event("easter-2025"):Image("rbxassetid://136636183189937"):Cost({
        ["Type"] = "Currency",
        ["Currency"] = "Coins",
        ["Amount"] = 1250000
    }):Pet(64, "Bunny Doggy"):Pet(30, "Egg Bunny"):Pet(3, "Angel Bunny"):Pet(0.02, "Seraphic Bunny"):Pet(0.001, "Ethereal Bunny"):Pet(0.0001, "Cardinal Bunny"):Pet(1e-6, "Easter Basket"):Build(),
    ["Throwback Egg"] = v3.new():Event("easter-2025"):Image("rbxassetid://118678260776654"):Cost({
        ["Type"] = "Currency",
        ["Currency"] = "Coins",
        ["Amount"] = 1400000
    }):Pet(64, "Bow Bunny"):Pet(30, "Easter Egg"):Pet(3, "Flying Bunny"):Pet(0.04, "Easter Serpent"):Pet(0.002, "Dualcorn"):Pet(0.0002, "Holy Egg"):Pet(4e-7, "Godly Gem"):Pet(1e-7, "Dementor"):Build(),
    ["100M Egg"] = v3.new():Event("event-100m"):Image("rbxassetid://72208201985111"):Cost({
        ["Type"] = "Currency",
        ["Currency"] = "Coins",
        ["Amount"] = 1000000
    }):Pet(64, "Bronze Bunny"):Pet(30, "Silver Fox"):Pet(3, "Golden Dragon"):Pet(0.04, "Diamond Serpent"):Pet(0.002, "Diamond Hexarium"):Pet(0.000125, "King Pufferfish"):Pet(2e-6, "Royal Trophy"):Build()
}
for v8, v9 in v7 do
    for _, v10 in v9.Pool do
        if v10.Item.Type == "Pet" then
            local v11 = v4[v10.Item.Name]
            if v11 then
                if v11.Rarity == "Legendary" or v11.Rarity == "Secret" then
                    for v12 = #v5.LegendaryTiers, 1, -1 do
                        if v10.Chance <= v5.LegendaryTiers[v12].Chance then
                            v11.Tier = v12
                            break
                        end
                    end
                    if not v11.Tier then
                        v11.Tier = 1
                    end
                end
                v11.Egg = v8
                v11.Chance = v10.Chance
            else
                warn((("Pet %* not valid in egg %*!"):format(v10.Item.Name, v8)))
            end
        end
    end
end
for _, v13 in v2:GetTagged("Egg") do
    local v14 = v13:GetAttribute("Island")
    if v14 then
        local v15 = v7[v13.Name]
        if v15 then
            v15.Island = v14
        end
    end
end
for v16, v17 in v6 do
    if v17.Type == "Egg" then
        local v18 = v7[v16]
        if not v18 then
            error((("%* is not a valid Egg"):format(v16)))
        end
        v17.Pool = v18.Pool
        v17.Icon = v18.Image
    end
end
return v7


-- Path to module: game:GetService("ReplicatedStorage").Shared.Data.Eggs