local v1 = game:GetService("ReplicatedStorage")
require(v1.Shared.Types)
local u2 = require(v1.Shared.Framework.Utilities.Math.Time)
local u3 = {
    ["QuestKey"] = "competitive",
    ["MaxPoints"] = 25000
}
local v4 = {
    ["Every"] = 500,
    ["Item"] = {
        ["Type"] = "Powerup",
        ["Name"] = "Series 1 Egg",
        ["Amount"] = 25
    }
}
u3.Bonus = v4
u3.RerollCost = {
    ["Type"] = "Powerup",
    ["Name"] = "Reroll Orb",
    ["Amount"] = 5
}
local v5 = {
    {
        {
            ["Type"] = "Pet",
            ["Name"] = "Elite Soul",
            ["Mythic"] = true
        },
        {
            ["Type"] = "Potion",
            ["Name"] = "Infinity Elixir",
            ["Amount"] = 30
        },
        {
            ["Type"] = "Powerup",
            ["Name"] = "Royal Key",
            ["Amount"] = 100
        },
        {
            ["Type"] = "Powerup",
            ["Name"] = "Golden Box",
            ["Amount"] = 50
        }
    },
    {
        {
            ["Type"] = "Pet",
            ["Name"] = "Elite Soul"
        },
        {
            ["Type"] = "Potion",
            ["Name"] = "Infinity Elixir",
            ["Amount"] = 15
        },
        {
            ["Type"] = "Powerup",
            ["Name"] = "Royal Key",
            ["Amount"] = 25
        },
        {
            ["Type"] = "Powerup",
            ["Name"] = "Golden Box",
            ["Amount"] = 25
        }
    },
    {
        {
            ["Type"] = "Pet",
            ["Name"] = "Elite Challenger"
        },
        {
            ["Type"] = "Potion",
            ["Name"] = "Infinity Elixir",
            ["Amount"] = 10
        },
        {
            ["Type"] = "Powerup",
            ["Name"] = "Golden Box",
            ["Amount"] = 10
        }
    }
}
u3.Placements = v5
local u6 = {}
local v7 = {
    ["FinishTime"] = DateTime.fromUniversalTime(2025, 5, 1, 0),
    ["Rewards"] = {
        {
            {
                ["Type"] = "Potion",
                ["Name"] = "Lucky",
                ["Level"] = 5,
                ["Amount"] = 10
            },
            {
                ["Type"] = "Potion",
                ["Name"] = "Speed",
                ["Level"] = 5,
                ["Amount"] = 10
            }
        },
        {
            {
                ["Type"] = "Powerup",
                ["Name"] = "Mystery Box",
                ["Amount"] = 10
            },
            {
                ["Type"] = "Powerup",
                ["Name"] = "Power Orb",
                ["Amount"] = 10
            }
        },
        {
            {
                ["Type"] = "Potion",
                ["Name"] = "Speed",
                ["Level"] = 6,
                ["Amount"] = 1
            },
            {
                ["Type"] = "Potion",
                ["Name"] = "Coins",
                ["Level"] = 6,
                ["Amount"] = 2
            }
        },
        {
            {
                ["Type"] = "Potion",
                ["Name"] = "Lucky",
                ["Level"] = 6,
                ["Amount"] = 2
            },
            {
                ["Type"] = "Powerup",
                ["Name"] = "Series 1 Egg",
                ["Amount"] = 25
            }
        },
        {
            {
                ["Type"] = "Potion",
                ["Name"] = "Lucky",
                ["Level"] = 6,
                ["Amount"] = 2
            },
            {
                ["Type"] = "Potion",
                ["Name"] = "Speed",
                ["Level"] = 6,
                ["Amount"] = 2
            }
        },
        {
            {
                ["Type"] = "Powerup",
                ["Name"] = "Series 1 Egg",
                ["Amount"] = 35
            },
            {
                ["Type"] = "Powerup",
                ["Name"] = "Golden Key",
                ["Amount"] = 25
            }
        },
        {
            {
                ["Type"] = "Powerup",
                ["Name"] = "Mystery Box",
                ["Amount"] = 10
            },
            {
                ["Type"] = "Potion",
                ["Name"] = "Lucky",
                ["Level"] = 6,
                ["Amount"] = 3
            }
        },
        {
            {
                ["Type"] = "Powerup",
                ["Name"] = "Golden Box",
                ["Amount"] = 10
            },
            {
                ["Type"] = "Powerup",
                ["Name"] = "Series 1 Egg",
                ["Amount"] = 35
            }
        },
        {
            {
                ["Type"] = "Powerup",
                ["Name"] = "Royal Key",
                ["Amount"] = 5
            },
            {
                ["Type"] = "Powerup",
                ["Name"] = "Mystery Box",
                ["Amount"] = 20
            }
        },
        {
            {
                ["Type"] = "Potion",
                ["Name"] = "Infinity Elixir",
                ["Amount"] = 1
            },
            {
                ["Type"] = "Powerup",
                ["Name"] = "Series 1 Egg",
                ["Amount"] = 50
            }
        }
    }
}
u6[1] = v7
function u3.GetSeason(_) --[[Anonymous function at line 102]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u6
    --]]
    local v8 = u2.now()
    for v9 = #u6, 1, -1 do
        if v8 < u6[v9].FinishTime.UnixTimestamp then
            return v9
        end
    end
    return nil
end
function u3.HasSeason(_) --[[Anonymous function at line 113]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    return u3:GetSeason() ~= nil
end
function u3.TrackComplete(_, p10) --[[Anonymous function at line 118]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u6
    --]]
    local v11 = u3:GetSeason()
    if not v11 then
        return false
    end
    local v12 = p10.Competitive
    if not v12 then
        return false
    end
    local v13 = u6[v11]
    return v12.Score >= u3:GetRequirement(v13, #v13.Rewards)
end
function u3.GetRequirement(_, p14, p15) --[[Anonymous function at line 135]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    local v16 = u3.MaxPoints * (p15 / #p14.Rewards) ^ 2 / 50
    return math.round(v16) * 50
end
u3.Seasons = u6
return u3
