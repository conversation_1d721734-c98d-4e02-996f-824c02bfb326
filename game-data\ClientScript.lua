--BOOIIII THIS IS SO TUFF FLIPPY SKIBIDI AURA (SIGMA SPY)
local u1 = game:GetService("ReplicatedStorage")
local u2 = game:GetService("Players")
local u3 = game:GetService("RunService")
local u4 = game:GetService("TweenService")
local u5 = game:GetService("HttpService")
local u6 = game:GetService("TextService")
local v7 = game:GetService("Workspace")
local u8 = game:GetService("CollectionService")
local u9 = game:GetService("MarketplaceService")
game:GetService("UserInputService")
local u10 = u2.LocalPlayer
u1.ScreenGui.Parent = u10.PlayerGui
local v11 = require(u1.Client.Gui.Animations.Intro)
task.spawn(v11.Play, v11)
require(u1.Client.Framework.Utilities.UserInput)
local u12 = require(u1.Shared.Framework.Network.Remote)
local v13 = require(u1.Client.Framework.Utilities.Gui.ScaleUI)
local u14 = require(u1.Client.Framework.Services.LocalData)
local v15 = require(u1.Shared.Framework.Utilities.every)
local u16 = require(u1.Shared.Framework.Utilities.String.FormatSuffix)
require(u1.Client.Gui.Utils.Shiny)
local u17 = require(u1.Client.Gui.GuiFrame)
require(u1.Client.Gui.Frames.GumShop)
local u18 = require(u1.Shared.Utils.AnimateColor)
local u19 = require(u1.Client.Gui.Tooltip)
local u20 = require(u1.Shared.Data.Builders.TooltipBuilder)
require(u1.Shared.Data.Flavors)
local u21 = require(u1.Shared.Data.Currency)
require(u1.Shared.Data.Pets)
local u22 = require(u1.Client.PetRender)
local u23 = require(u1.Client.PetRender.VisualPet)
require(u1.Shared.Types)
local u24 = require(u1.Client.Activation)
local u25 = require(u1.Client.Gui.PlayTransition)
require(u1.Client.Gui.Frames.WorldMap)
local u26 = require(u1.Client.Gui.Utils.AutoButtonColor)
local u27 = require(u1.Shared.Palette)
require(u1.Client.Gui.Frames.Index)
local u28 = require(u1.Client.Gui.GetScreenGuiHolder)
local u29 = require(u1.Shared.Utils.Stats.ItemUtil)
local u30 = require(u1.Shared.Framework.Utilities.Math.GetPositionOnArc)
local u31 = require(u1.Shared.Framework.Utilities.CharacterUtil)
local u32 = require(u1.Shared.Framework.Utilities.EmitParticleAtPosition)
local u33 = require(u1.Client.Gui.LootPoolViewer)
local u34 = require(u1.Shared.Data.Eggs)
local u35 = require(u1.Shared.Framework.Utilities.Math.Time)
local v36 = require(u1.Shared.Utils.Chunker)
local u37 = require(u1.Client.Gui.Utils.PlayLocalSound)
local u38 = require(u1.Client.Effects.HatchEgg)
local u39 = require(u1.Client.Effects.PhysicalItem)
local u40 = require(u1.Client.Gui.Prompt)
local u41 = require(u1.Shared.Data.Builders.PromptBuilder)
local u42 = require(u1.Client.Gui.Utils.ClickableButton)
require(u1.Shared.Data.Builders.AchievementBuilder)
local u43 = require(u1.Shared.Utils.RichText)
require(u1.Client.Gui.Utils.Achievement)
local u44 = require(u1.Shared.Utils.Stats.StatsUtil)
local v45 = require(u1.Shared.Utils.GetPickupZones)
local u46 = require(u1.Shared.Constants)
require(u1.Client.Gui.Utils.Notification)
local v47 = require(u1.Shared.Framework.Classes.Pool)
require(u1.Client.Music)
local u48 = require(u1.Shared.Utils.GetInfinityEgg)
local v49 = require(u1.Client.Effects.RenderAnimatedObject)
local u50 = require(u1.Client.Gui.ProximityPrompt)
local u51 = require(u1.Shared.Utils.SetModelUnlocked)
local u52 = require(u1.Shared.Data.Builders.UnlockBuilder)
local u53 = require(u1.Client.Gui.Unlock)
require(u1.Client.Gui.Animations.ItemFall)
local v54 = require(u1.Shared.Utils.WorldUtil)
local u55 = require(u1.Client.LowDetail)
require(u1.Client.Effects.OutlinePulse)
local u56 = require(u1.Shared.Utils.RaceUtil)
local u57 = require(u1.Client.Effects.InterestMark)
local u58 = require(u1.Shared.Framework.Services.CollisionGroup)
require(u1.Shared.Data.Worlds)
local u59 = require(u1.Client.Gui.Frames.Enchants)
require(u1.Shared.Framework.Classes.Zone)
local v60 = require(u1.Client.Framework.Utilities.MakeDynamicNPC)
require(u1.Client.Effects.Physics)
local u61 = require(u1.Client.Gui.Utils.Timer)
local u62 = require(u1.Shared.Data.Rifts)
require(u1.Shared.Framework.Utilities.String.FormatCommas)
local u63 = require(u1.Shared.Framework.Utilities.SequenceUtil)
local u64 = require(u1.Client.Gui.Frames.ItemShop)
local u65 = require(u1.Client.Effects.Particles.Fireworks)
local u66 = require(u1.Client.Effects.Particles.Splash)
local u67 = require(u1.Client.Tutorial)
local u68 = require(u1.Shared.Framework.Utilities.ProductInfo)
local u69 = require(u1.Client.Framework.Utilities.Gui.Animations.SetFade)
local u70 = require(u1.Client.Gui.Animations.ProcessingPurchase)
local u71 = require(u1.Client.Gui.Utils.CheckInventorySpace)
require(u1.Shared.Data.Titles)
require(u1.Client.Gui.Utils.ItemFrame)
local u72 = u10.PlayerGui.ScreenGui
local _ = workspace.CurrentCamera
u72.VersionLabel.Text = ("v%*"):format(game.PlaceVersion)
local v73 = workspace.Markers.Pets
local u74 = {}
local function u84() --[[Anonymous function at line 120]]
    --[[
    Upvalues:
        [1] = u74
    --]]
    local v75 = {}
    for v76, v77 in u74 do
        local v78 = v77.visual.Owner
        if not v75[v78] then
            v75[v78] = {}
        end
        local v79 = v75[v78]
        table.insert(v79, v76)
    end
    for _, v80 in v75 do
        table.sort(v80)
    end
    for _, v81 in v75 do
        for v82, v83 in v81 do
            u74[v83].visual.GroupIndex = v82
            u74[v83].visual.GroupCount = #v81
        end
    end
end
local function v89(p85) --[[Anonymous function at line 140]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u2
        [3] = u23
        [4] = u22
        [5] = u74
        [6] = u84
    --]]
    local v86 = ("%*:%*"):format(p85.Name, (u5:GenerateGUID(false)))
    p85.Name = v86
    local v87 = u2:GetPlayerByUserId(p85:GetAttribute("OwnerId"))
    if v87 then
        local v88 = u23.new({
            ["GUID"] = v86,
            ["Name"] = p85:GetAttribute("Name"),
            ["Shiny"] = p85:GetAttribute("Shiny"),
            ["Mythic"] = p85:GetAttribute("Mythic")
        }, v87)
        u22:Add(v88)
        u74[v86] = {
            ["visual"] = v88,
            ["connections"] = {}
        }
        u84()
    end
end
local function v94(p90) --[[Anonymous function at line 167]]
    --[[
    Upvalues:
        [1] = u22
        [2] = u74
        [3] = u84
    --]]
    local v91 = p90.Name
    u22:RemoveByGUID(v91)
    local v92 = u74[v91]
    if v92 ~= nil then
        for _, v93 in v92.connections do
            v93:Disconnect()
        end
        u74[v91] = nil
    end
    u84()
end
for _, v95 in v73:GetChildren() do
    v89(v95)
end
v73.ChildAdded:Connect(v89)
v73.ChildRemoved:Connect(v94)
local u96 = u1.Assets.VisualItem
local u97 = Random.new()
local u98 = TweenInfo.new(0.5, Enum.EasingStyle.Quint)
local u99 = TweenInfo.new(0.25, Enum.EasingStyle.Circular)
local u100 = TweenInfo.new(0.25, Enum.EasingStyle.Circular, Enum.EasingDirection.In)
local function u125(p101, p102, u103) --[[Anonymous function at line 206]]
    --[[
    Upvalues:
        [1] = u96
        [2] = u28
        [3] = u29
        [4] = u97
        [5] = u30
        [6] = u3
        [7] = u37
        [8] = u4
        [9] = u98
        [10] = u31
        [11] = u10
        [12] = u99
        [13] = u100
        [14] = u32
        [15] = u1
        [16] = u12
    --]]
    local u104 = u96:Clone()
    u104.Name = p101
    u104.Transparency = 1
    u104.CanCollide = false
    u104.Parent = workspace.Rendered.Generic
    local v105 = u104.BillboardGui
    v105.Adornee = u104
    v105.Parent = u28()
    u29:UpdateIcon(v105.Icon, p102)
    local u106 = Instance.new("NumberValue")
    local u107 = Instance.new("NumberValue")
    local v108 = u103 + u97:NextUnitVector() * Vector3.new(1, 0, 1) * 5
    local v109 = 3 + u97:NextNumber() * 3
    local u110 = v108 + Vector3.new(0, v109, 0)
    local u111 = 0
    local function v117(p112) --[[Anonymous function at line 227]]
        --[[
        Upvalues:
            [1] = u111
            [2] = u104
            [3] = u30
            [4] = u103
            [5] = u110
            [6] = u106
            [7] = u107
        --]]
        u111 = u111 + p112 * 10
        local v113 = u104
        local v114 = CFrame.new(u30(u103, u110, 3, u106.Value))
        local v115 = u111
        local v116 = math.sin(v115) * u107.Value
        v113.CFrame = v114 + Vector3.new(0, v116, 0)
    end
    local v118 = u3.Heartbeat:Connect(v117)
    u111 = u111 + 0
    local v119 = CFrame.new(u30(u103, u110, 3, u106.Value))
    local v120 = u111
    local v121 = math.sin(v120) * u107.Value
    u104.CFrame = v119 + Vector3.new(0, v121, 0)
    u37("PetMove")
    u4:Create(u106, u98, {
        ["Value"] = 1
    }):Play()
    task.wait(u98.Time)
    u4:Create(u107, u98, {
        ["Value"] = 0.1
    }):Play()
    task.wait(u98.Time + 0.5)
    v118:Disconnect()
    u4:Create(u107, u98, {
        ["Value"] = 0
    }):Play()
    local v122 = u31:GetRootPart(u10)
    if v122 then
        local v123 = (v122.Position - u110).Unit * 4
        u4:Create(u104, u99, {
            ["CFrame"] = CFrame.new(u110) - v123
        }):Play()
        task.wait(u99.Time)
        local v124 = CFrame.new(v122.Position) + u97:NextUnitVector()
        u4:Create(u104, u100, {
            ["CFrame"] = v124
        }):Play()
        task.wait(u100.Time)
        u32(v124.Position, u1.Assets.Particles.VisualItemCollect, 5)
    end
    u107:Destroy()
    u106:Destroy()
    u104:Destroy()
    v105:Destroy()
    u12:FireServer("GrabVisualItem", { p101 })
end
u12.Event("RenderVisualItems"):Connect(function(p126, p127) --[[Anonymous function at line 266]]
    --[[
    Upvalues:
        [1] = u125
    --]]
    for v128, v129 in p127 do
        if v129.Type ~= "Pet" then
            task.defer(u125, v128, v129, p126)
        end
        task.wait(0.15)
    end
end)
local u130 = 0
local u131 = Random.new()
local u132 = {}
local u133 = v36.new(128, 2)
u133.Mode = "3D"
local function u145(p134, p135) --[[Anonymous function at line 301]]
    --[[
    Upvalues:
        [1] = u130
    --]]
    local v136 = p134:GetAttribute("Offset") or 0
    local v137 = p134:GetAttribute("Size")
    if not v137 then
        v137 = p134:GetExtentsSize()
        p134:SetAttribute("Size", v137)
    end
    local v138 = u130 + v136
    local v139 = math.cos(v138) + 0.5
    local v140 = CFrame.new(p135)
    local v141 = CFrame.Angles
    local v142 = u130 * 5 + v136
    local v143 = v140 * v141(0, math.rad(v142), 0)
    local v144 = v137.Y / 2 + v139 / 6 + 0.25
    p134:PivotTo(v143 + Vector3.new(0, v144, 0))
end
local function u157(p146, p147) --[[Anonymous function at line 316]]
    --[[
    Upvalues:
        [1] = u14
        [2] = u132
        [3] = u34
        [4] = u51
    --]]
    local v148 = u14:Get()
    if v148 then
        local v149 = u132[p146]
        if v149 then
            local v150 = u34[p146.Name]
            local v151
            if p146.Name == "Coming Soon" then
                v151 = false
            else
                v151 = (p146.Name == "Infinity Egg" or not (v150.Island or v150.World)) and true or p147
            end
            if v150 then
                v150 = v150.Island
            end
            local v152 = v150 == nil and true or v148.AreasUnlocked[v150] ~= nil
            local v153 = v148.EggsOpened[p146.Name] and true or v152
            local v154 = p146.Prompt:FindFirstChildOfClass("BillboardGui")
            if v154 then
                v154.Enabled = not v153
            end
            if v149.VisualLock then
                if v151 or p146.Name == "Coming Soon" then
                    v149.VisualLock:Disable()
                else
                    v149.VisualLock:Enable()
                end
            end
            local v155 = v149.Prompt
            local v156
            if v151 or (not v153 or p146.Name == "Coming Soon") then
                v156 = nil
            else
                v156 = p146.Prompt or nil
            end
            v155.Parent = v156
            u51(p146, v151)
            u51(v149.Model, v151)
            if p146.Name == "Coming Soon" then
                p146.Plate.SurfaceGui.Enabled = true
            end
            p146:SetAttribute("Unlocked", v151)
        end
    else
        return
    end
end
local function v161(p158) --[[Anonymous function at line 472]]
    --[[
    Upvalues:
        [1] = u55
        [2] = u132
        [3] = u145
        [4] = u130
    --]]
    if not u55.Enabled then
        for v159, v160 in u132 do
            if v159.Name ~= "Coming Soon" then
                u145(v160.Model, v159.Root.Position)
            end
        end
        u130 = u130 + p158 * 2
    end
end
u10.CharacterAdded:Connect(function() --[[Anonymous function at line 484]]
    --[[
    Upvalues:
        [1] = u31
        [2] = u10
        [3] = u133
    --]]
    local v162 = u31:GetRootPart(u10)
    if v162 then
        u133:Update(v162.Position)
    end
end)
u133.Loaded:Connect(function(u163) --[[Function name: create, line 360]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u14
        [3] = u34
        [4] = u67
        [5] = u50
        [6] = u29
        [7] = u41
        [8] = u40
        [9] = u71
        [10] = u24
        [11] = u12
        [12] = u16
        [13] = u57
        [14] = u131
        [15] = u132
        [16] = u145
        [17] = u157
    --]]
    local v164 = u1.Assets.Eggs:FindFirstChild(u163.Name)
    if u163.Name == "Coming Soon" then
        v164 = u1.Assets.Eggs["Common Egg"]
    end
    if v164 then
        local v165 = u14:Get()
        if not v165 then
            repeat
                task.wait(0.1)
                v165 = u14:Get()
            until v165
        end
        local u166 = u34[u163.Name]
        local v167 = v164:Clone()
        local v168 = u163.Name == "Infinity Egg" and 2.75 or 1.5
        local v169 = u163.Prompt:FindFirstChildOfClass("Attachment")
        if u163.Name == "Common Egg" and v169 then
            u67._tutorialEggAttachment = v169
        elseif u163.Name == "Spotted Egg" and v169 then
            u67._tutorialEgg2Attachment = v169
        end
        local v170 = u50.new()
        v170.MaxActivationDistance = 6.5
        v170.ActionText = u163.Name
        v170.Triggered:Connect(function() --[[Anonymous function at line 395]]
            --[[
            Upvalues:
                [1] = u166
                [2] = u14
                [3] = u29
                [4] = u41
                [5] = u163
                [6] = u40
                [7] = u71
                [8] = u24
                [9] = u12
            --]]
            if u166 then
                local v171 = u14:Get()
                if v171 then
                    if u29:GetOwnedAmount(v171, u166.Cost) < u29:GetAmount(u166.Cost) then
                        local v173 = u41.fromNotEnough(v171, u166.Cost, function(p172) --[[Anonymous function at line 406]]
                            --[[
                            Upvalues:
                                [1] = u163
                            --]]
                            return ("You need %* to unlock the %*."):format(p172, u163.Name)
                        end)
                        v173:Gamepass("Triple Hatch")
                        return u40(v173:Build())
                    elseif u71(v171) then
                        u24:Beam(u163, 10)
                        u12:FireServer("HatchEgg", u163.Name, 1)
                    end
                else
                    return
                end
            else
                return
            end
        end)
        if u166 and (u166.Island and not u163.Prompt:FindFirstChildOfClass("BillboardGui")) then
            local v174 = u1.Assets.EggLock:Clone()
            v174.Label.Text = u166.Island
            v174.Parent = u163.Prompt
        end
        if u166 then
            v170.ObjectText = ("%* %*"):format(u16(u29:GetAmount(u166.Cost)), (u29:GetName(u166.Cost)))
        end
        local v175 = u57(u1.Assets.VisualEggLock, u163.Root.Position + Vector3.new(0, 5, 0))
        v167:ScaleTo(v168)
        v167:SetAttribute("Offset", u131:NextNumber() * 5)
        v167.Parent = workspace.Rendered.Generic
        u132[u163] = {
            ["Model"] = v167,
            ["Prompt"] = v170,
            ["Locked"] = nil,
            ["VisualLock"] = v175
        }
        u145(v167, u163.Root.Position)
        u157(u163, v165.EggsOpened[u163.Name] ~= nil)
    end
end)
u133.Unloaded:Connect(function(p176) --[[Function name: remove, line 451]]
    --[[
    Upvalues:
        [1] = u132
    --]]
    local v177 = u132[p176]
    if v177 then
        if v177.VisualLock then
            v177.VisualLock:Destroy()
        end
        v177.Model:Destroy()
        v177.Prompt:Destroy()
        u132[p176] = nil
    end
end)
u3.Heartbeat:Connect(v161)
local function u190(u178) --[[Anonymous function at line 492]]
    --[[
    Upvalues:
        [1] = u34
        [2] = u48
        [3] = u10
        [4] = u68
        [5] = u46
        [6] = u21
        [7] = u16
        [8] = u29
        [9] = u6
        [10] = u8
        [11] = u132
        [12] = u133
    --]]
    task.wait(1.5)
    local v179 = string.split(u178.Name, " ")[1] or "Unknown"
    u178.Prompt.Transparency = 1
    u178.Root.Transparency = 1
    u178.Plate.SurfaceGui.Label.Text = string.upper(v179)
    if u178.Name == "Coming Soon" then
        u178.Plate.SurfaceGui.Label.Text = "COMING"
        u178.Plate.SurfaceGui.Egg.Text = "SOON"
    end
    local function v186() --[[Anonymous function at line 504]]
        --[[
        Upvalues:
            [1] = u178
            [2] = u34
            [3] = u48
            [4] = u10
            [5] = u68
            [6] = u46
            [7] = u21
            [8] = u16
            [9] = u29
            [10] = u6
        --]]
        task.wait()
        local u180 = u178.Cost.SurfaceGui.Cost.Label
        local v181 = nil
        if u178.Name == "Infinity Egg" then
            local v182 = u48(u10)
            if v182 then
                v181 = v182.Cost
            end
        else
            v181 = (u34[u178.Name] or {
                ["Cost"] = {
                    ["Type"] = "Currency",
                    ["Currency"] = "Coins",
                    ["Amount"] = 1
                }
            }).Cost
        end
        local v183 = u34[u178.Name]
        if v183 then
            v183 = v183.ProductId
        end
        if v183 then
            u178.Cost.SurfaceGui.Cost.Icon.Visible = false
            u68.fromProduct(v183, function(p184) --[[Anonymous function at line 525]]
                --[[
                Upvalues:
                    [1] = u180
                    [2] = u46
                --]]
                u180.Text = ("%*%*"):format(u46.RobuxUnicode, p184 and p184.PriceInRobux or "__")
                u180.TextColor3 = Color3.fromRGB(118, 255, 108)
            end)
        elseif v181 then
            u180.TextColor3 = u21[v181.Currency].Color
            u180.Text = u16(u29:GetAmount(v181), 10000)
            u29:UpdateIcon(u178.Cost.SurfaceGui.Cost.Icon, v181)
        else
            u180.Text = "???"
        end
        local v185 = u6:GetTextSize(u180.Text, u180.TextSize, u180.Font, Vector2.new((1 / 0), 0))
        u180.Size = UDim2.new(0, v185.X, 1, 0)
    end
    u8:RemoveTag(u178, "Egg")
    u178.Destroying:Connect(function() --[[Anonymous function at line 544]]
        --[[
        Upvalues:
            [1] = u178
            [2] = u132
            [3] = u133
        --]]
        local v187 = u178:GetPivot().Position
        local v188 = u178
        local v189 = u132[v188]
        if v189 then
            if v189.VisualLock then
                v189.VisualLock:Destroy()
            end
            v189.Model:Destroy()
            v189.Prompt:Destroy()
            u132[v188] = nil
        end
        u133:Remove(v187, u178)
    end)
    v186()
    u133:Add(u178:GetPivot().Position, u178)
    return v186
end
local function v192() --[[Anonymous function at line 464]]
    --[[
    Upvalues:
        [1] = u31
        [2] = u10
        [3] = u133
    --]]
    local v191 = u31:GetRootPart(u10)
    if v191 then
        u133:Update(v191.Position)
    end
end
local u193 = false
local u194 = nil
for _, v195 in u8:GetTagged("Egg") do
    if v195.Name ~= "Infinity Egg" then
        task.defer(u190, v195)
    end
end
u8:GetInstanceAddedSignal("Egg"):Connect(function(p196) --[[Anonymous function at line 562]]
    --[[
    Upvalues:
        [1] = u190
    --]]
    if p196.Name ~= "Infinity Egg" then
        task.defer(u190, p196)
    end
end)
u14:ConnectDataChanged({
    "Stats",
    "TotalEggsOpened",
    "Pets",
    "Teams",
    "TeamEquipped"
}, (u190(workspace.Worlds["The Overworld"]["Infinity Egg"])))
u14:ConnectDataChanged({ "EggsOpened", "AreasUnlocked" }, function() --[[Anonymous function at line 571]]
    --[[
    Upvalues:
        [1] = u14
        [2] = u132
        [3] = u157
    --]]
    local v197 = u14:Get()
    if v197 then
        for v198, _ in u132 do
            u157(v198, v197.EggsOpened[v198.Name] ~= nil)
        end
    end
end)
local v199 = u31:GetRootPart(u10)
if v199 then
    u133:Update(v199.Position)
end
v15(0.25, v192)
script:WaitForChild("GetAutoHatchStatus").OnInvoke = function() --[[Function name: OnInvoke, line 586]]
    --[[
    Upvalues:
        [1] = u193
        [2] = u194
    --]]
    if u193 and u194 then
        return u194.Name
    else
        return nil
    end
end
local u200 = script:WaitForChild("EggHatchRejoinActivation")
local u201 = nil
v15(0.1, function() --[[Anonymous function at line 601]]
    --[[
    Upvalues:
        [1] = u31
        [2] = u10
        [3] = u193
        [4] = u14
        [5] = u50
        [6] = u132
        [7] = u48
        [8] = u34
        [9] = u44
        [10] = u29
        [11] = u38
        [12] = u41
        [13] = u40
        [14] = u35
        [15] = u194
        [16] = u71
        [17] = u12
        [18] = u19
        [19] = u33
        [20] = u70
        [21] = u9
        [22] = u201
        [23] = u200
    --]]
    if u31:IsAlive(u10) then
        local v202 = u31:GetRootPart(u10)
        if v202 then
            local u203 = u14:Get()
            if u203 then
                local u204 = nil
                local v205 = (1 / 0)
                if u50.Visible == 0 then
                    for v206 in u132 do
                        local v207 = (v206.Root.Position - v202.Position).Magnitude
                        if v207 <= 16 then
                            if v206:GetAttribute("Unlocked") then
                                if v207 < v205 then
                                    u204 = v206
                                    v205 = v207
                                end
                            end
                        end
                    end
                end
                if u204 and u193 then
                    local u208 = nil
                    if u204.Name == "Infinity Egg" then
                        local v209 = u48(u10)
                        if v209 then
                            u208 = v209.Cost
                        end
                    else
                        u208 = u34[u204.Name].Cost
                    end
                    local v210 = u44:GetMaxEggHatches(u203)
                    if u29:GetOwnedAmount(u203, u208) < u29:GetAmount(u208) * v210 then
                        task.spawn(function() --[[Anonymous function at line 652]]
                            --[[
                            Upvalues:
                                [1] = u38
                                [2] = u41
                                [3] = u203
                                [4] = u208
                                [5] = u29
                                [6] = u40
                            --]]
                            repeat
                                task.wait(0.1)
                            until not u38:IsHatching()
                            local v211 = u41.fromNotEnough(u203, u208, function() --[[Anonymous function at line 654]]
                                --[[
                                Upvalues:
                                    [1] = u29
                                    [2] = u208
                                --]]
                                return ("You don\'t have enough %* to auto hatch!"):format((u29:GetName(u208)))
                            end)
                            v211:Gamepass("Triple Hatch")
                            u40(v211:Build())
                        end)
                        u193 = false
                    end
                end
                local v212 = u10:GetAttribute("NextEggOpenAt") or 0
                if u194 and (u193 and (u35.now() - v212 >= 0 and not u38:IsHatching())) then
                    local v213 = u44:GetMaxEggHatches(u203)
                    if not u71(u203) then
                        u193 = false
                        return
                    end
                    u12:FireServer("HatchEgg", u194.Name, v213)
                end
                if u194 ~= u204 then
                    u19:Hide()
                    local v214 = u33
                    local v215
                    if u204 then
                        v215 = u204.Prompt or nil
                    else
                        v215 = nil
                    end
                    v214:Attach(v215)
                    if u204 then
                        local v216 = u34[u204.Name]
                        if v216 and v216.ProductId then
                            u33.fromProductEgg(u204.Name)
                        elseif u204.Name == "Infinity Egg" then
                            u33.fromInfinityEgg()
                        else
                            u33.fromEgg(u204.Name, u204:GetAttribute("BonusLuck"))
                        end
                        local function u227(p217, p218) --[[Anonymous function at line 698]]
                            --[[
                            Upvalues:
                                [1] = u34
                                [2] = u14
                                [3] = u44
                                [4] = u193
                                [5] = u48
                                [6] = u10
                                [7] = u29
                                [8] = u71
                                [9] = u70
                                [10] = u9
                                [11] = u41
                                [12] = u40
                                [13] = u12
                            --]]
                            local v219 = u34[p218]
                            if p218 == "Infinity Egg" or v219 then
                                local v220 = u14:Get()
                                if v220 then
                                    local v221 = u44:GetMaxEggHatches(v220)
                                    if p217 == 2 then
                                        u193 = true
                                    end
                                    local v222 = p217 == 3 and 1 or v221
                                    local v223 = nil
                                    if p218 == "Infinity Egg" then
                                        local v224 = u48(u10)
                                        if v224 then
                                            v223 = v224.Cost
                                        end
                                    elseif v219 then
                                        v223 = v219.Cost
                                    end
                                    if v223 and p217 ~= 3 then
                                        while v222 > 0 and u29:GetOwnedAmount(v220, v223) < u29:GetAmount(v223) * v222 do
                                            v222 = v222 - 1
                                        end
                                    end
                                    if u71(v220) then
                                        if v219 and v219.ProductId then
                                            u70:Show()
                                            u9:PromptProductPurchase(u10, v219.ProductId)
                                        else
                                            if v222 <= 0 or v223 and u29:GetOwnedAmount(v220, v223) < u29:GetAmount(v223) then
                                                local v226 = u41.fromNotEnough(v220, v223, function(p225) --[[Anonymous function at line 741]]
                                                    return ("You need %* to hatch this egg."):format(p225)
                                                end)
                                                v226:Gamepass("Triple Hatch")
                                                return u40(v226:Build())
                                            end
                                            u12:FireServer("HatchEgg", p218, v222)
                                        end
                                    else
                                        return
                                    end
                                else
                                    return
                                end
                            else
                                return
                            end
                        end
                        u33.Activated:Connect(function(p228) --[[Anonymous function at line 752]]
                            --[[
                            Upvalues:
                                [1] = u227
                                [2] = u204
                            --]]
                            u227(p228, u204.Name)
                        end)
                        local function v230(p229) --[[Anonymous function at line 756]]
                            --[[
                            Upvalues:
                                [1] = u227
                            --]]
                            u227(2, p229)
                        end
                        if u201 then
                            u201:Disconnect()
                            u201 = nil
                        end
                        u201 = u200.Event:Connect(v230)
                    end
                    u194 = u204
                    u193 = false
                end
            else
                u193 = false
                return
            end
        else
            u193 = false
            return
        end
    else
        u193 = false
        return
    end
end)
local u231 = 0
local function u241(p232, _, p233) --[[Anonymous function at line 771]]
    --[[
    Upvalues:
        [1] = u231
        [2] = u1
        [3] = u28
        [4] = u4
        [5] = u69
        [6] = u37
    --]]
    local v234 = os.clock()
    if v234 - u231 >= 0.1 then
        u231 = v234
        local v235 = p232.Model:GetPivot().Position
        local v236 = Instance.new("Part")
        v236.Anchored = true
        v236.Size = Vector3.new(1, 1, 1)
        v236.CanCollide = false
        v236.Transparency = 1
        v236.CFrame = CFrame.new(v235)
        v236.Parent = p232.Model
        local v237 = u1.Assets.LevelUp:Clone()
        v237.Inner.UIScale.Scale = 1.5
        v237.Inner.Level.Text = p233
        v237.Parent = u28()
        v237.Adornee = v236
        local v238 = TweenInfo.new(0.25, Enum.EasingStyle.Quint)
        local v239 = TweenInfo.new(1.5, Enum.EasingStyle.Sine)
        local v240 = TweenInfo.new(0.5, Enum.EasingStyle.Sine)
        u4:Create(v237, v239, {
            ["StudsOffsetWorldSpace"] = Vector3.new(0, 4, 0)
        }):Play()
        u4:Create(v237.Inner.UIScale, v238, {
            ["Scale"] = 1
        }):Play()
        u69(v237.Inner, 0, v238)
        u37("LevelUp", v236.Position)
        task.wait(v238.Time + v239.Time - v240.Time)
        u69(v237.Inner, 1, v240)
        task.wait(v240.Time)
        v236:Destroy()
        v237:Destroy()
    end
end
u12.Event("PetLevelsUpdated"):Connect(function(p242) --[[Anonymous function at line 816]]
    --[[
    Upvalues:
        [1] = u22
        [2] = u241
    --]]
    for _, v243 in p242 do
        local v244 = nil
        for v245, v246 in u22.Pets do
            local v247 = string.split
            local v248, _ = unpack(v247(v245, ":"))
            if v248 == v243.NewId then
                v244 = v246
                break
            end
        end
        if v244 then
            task.spawn(u241, v244, v243.OldLevel, v243.NewLevel)
        end
    end
end)
local v249 = u1.Remotes.Pickups.SpawnPickups
local u250 = u1.Remotes.Pickups.CollectPickup
local u251 = v7.Rendered.Pickups
local u252 = v45()
local u253 = {}
local u254 = {}
u67._activePickups = u253
local u255 = v36.new(128, 2)
u255.Mode = "3D"
local u256 = TweenInfo.new(0.5, Enum.EasingStyle.Quint)
local u257 = Random.new()
local function u267(p258) --[[Anonymous function at line 854]]
    --[[
    Upvalues:
        [1] = u31
        [2] = u10
        [3] = u58
        [4] = u3
        [5] = u257
        [6] = u4
        [7] = u256
        [8] = u32
        [9] = u1
    --]]
    local v259 = u31:GetRootPart(u10)
    if v259 then
        local u260 = p258:Clone()
        u260.Parent = workspace.Rendered.Generic
        u58:AddModel(u260, "Ignore")
        local u261 = Instance.new("NumberValue")
        u261.Value = 1
        local u262 = Instance.new("CFrameValue")
        u262.Value = p258:GetPivot()
        local u263 = p258:GetScale()
        local v264 = u3.Heartbeat:Connect(function() --[[Anonymous function at line 871]]
            --[[
            Upvalues:
                [1] = u260
                [2] = u262
                [3] = u261
                [4] = u263
            --]]
            u260:PivotTo(u262.Value)
            u260:ScaleTo(u261.Value * u263)
        end)
        local v265 = u257:NextUnitVector() * 6.283185307179586
        for _, v266 in u260:GetDescendants() do
            if v266:IsA("BasePart") and v266.Transparency < 1 then
                v266.CanCollide = false
                u4:Create(v266, u256, {
                    ["Transparency"] = 1
                }):Play()
            end
        end
        u4:Create(u261, u256, {
            ["Value"] = 0.1
        }):Play()
        u4:Create(u262, u256, {
            ["Value"] = CFrame.new(v259.Position) * CFrame.Angles(v265.X, v265.Y, v265.Z)
        }):Play()
        u32(u262.Value.Position, u1.Assets.Particles.Pickup, 6)
        task.wait(u256.Time)
        u260:Destroy()
        u261:Destroy()
        u262:Destroy()
        v264:Disconnect()
    end
end
local u268 = 0
u14:ConnectDataChanged({
    "Pets",
    "Teams",
    "TeamEquipped",
    "MasteryLevels",
    "MasteryUpgrades",
    "ActiveBuffs"
}, function(p269) --[[Anonymous function at line 897]]
    --[[
    Upvalues:
        [1] = u268
        [2] = u44
    --]]
    u268 = u44:GetPickupRange(p269)
end)
local function v276() --[[Anonymous function at line 901]]
    --[[
    Upvalues:
        [1] = u31
        [2] = u10
        [3] = u14
        [4] = u268
        [5] = u253
        [6] = u267
        [7] = u255
        [8] = u250
    --]]
    local v270 = u31:GetRootPart(u10)
    if v270 then
        if u14:Get() then
            local v271 = v270.Position
            local v272 = u268
            for v273, v274 in u253 do
                if (v274:GetPivot().Position - v271).Magnitude <= v272 then
                    task.spawn(u267, v274)
                    for _, v275 in v274:GetChildren() do
                        if v275:IsA("Highlight") then
                            v275.Parent = nil
                        end
                    end
                    v274:Destroy()
                    u255:Remove(v274:GetPivot().Position, v274)
                    u250:FireServer(v273)
                end
            end
            u255:Update(v270.Position)
        end
    else
        return
    end
end
u255.Loaded:Connect(function(p277) --[[Anonymous function at line 938]]
    --[[
    Upvalues:
        [1] = u254
        [2] = u253
    --]]
    if p277:GetAttribute("Animated") then
        u254[p277] = p277:GetPivot().Position
    end
    u253[p277.Name] = p277
end)
u255.Unloaded:Connect(function(p278) --[[Anonymous function at line 945]]
    --[[
    Upvalues:
        [1] = u253
        [2] = u254
    --]]
    u253[p278.Name] = nil
    u254[p278] = nil
end)
v249.OnClientEvent:Connect(function(p279) --[[Anonymous function at line 950]]
    --[[
    Upvalues:
        [1] = u252
        [2] = u1
        [3] = u58
        [4] = u251
        [5] = u255
    --]]
    for _, v280 in p279 do
        local v281 = u252[v280.Root]
        Vector3.new()
        local v282 = v281.Zone
        local v283
        if typeof(v282) == "table" then
            local v284 = v281.Zone:GetRandomPoint()
            local v285 = v281.Zone.Height
            v283 = v284 + Vector3.new(0, v285, 0)
        else
            local v286 = v281.Zone.Position
            local v287 = v281.Zone.Size.Y / 2
            v283 = v286 - Vector3.new(0, v287, 0)
        end
        local v288 = u1.Assets.Pickups:FindFirstChild(v280.Visual)
        local v289
        if v288 then
            v289 = v288:Clone()
        else
            warn((("pickup model \"%*\" doesn\'t exist"):format(v280.Visual)))
            v289 = Instance.new("Model")
            local v290 = Instance.new("Part")
            v290.Anchored = true
            v290.CanCollide = false
            v290.BrickColor = BrickColor.Random()
            v290.Material = Enum.Material.Neon
            v290.Shape = Enum.PartType.Ball
            v290.Size = Vector3.new(2, 2, 2)
            v290.Parent = v289
        end
        if v289.Name == "Coin" or v289.Name == "Gem" then
            v289:SetAttribute("Animated", true)
        end
        for _, v291 in v289:GetDescendants() do
            if v291:IsA("BasePart") then
                v291.CastShadow = false
            end
        end
        u58:AddModel(v289, "Ignore")
        v289:PivotTo(CFrame.new(v283) * CFrame.Angles(0, math.random() * 3.141592653589793 * 2, 0))
        v289.Name = v280.Id
        v289.Parent = u251
        u255:Add(v283, v289)
    end
end)
u3.Heartbeat:Connect(function(p292) --[[Anonymous function at line 1002]]
    --[[
    Upvalues:
        [1] = u55
        [2] = u254
    --]]
    if not u55.Enabled then
        for v293, _ in u254 do
            local v294 = v293:GetPivot()
            local v295 = CFrame.Angles
            local v296 = p292 * 64
            v293:PivotTo(v294 * v295(0, math.rad(v296), 0))
        end
    end
end)
v15(0.1, v276)
local u297 = workspace.Worlds["The Overworld"].LikeBoard.Display.SurfaceGui
u297.Adornee = workspace.Worlds["The Overworld"].LikeBoard.Display
u297.Parent = u28()
local u298 = u297.Items.Template
u298.Parent = nil
local u302 = v47.new(function() --[[Anonymous function at line 1026]]
    --[[
    Upvalues:
        [1] = u298
        [2] = u19
        [3] = u17
        [4] = u5
        [5] = u20
    --]]
    local u299 = u298:Clone()
    u19:OnEnter(u299.Hover, function() --[[Anonymous function at line 1028]]
        --[[
        Upvalues:
            [1] = u17
            [2] = u299
            [3] = u5
            [4] = u20
        --]]
        if u17:IsAnyOpen() then
            return
        else
            local v300 = u299:GetAttribute("Item")
            if v300 and typeof(v300) == "string" then
                local v301 = u5:JSONDecode(v300)
                if v301 then
                    return u20.fromItem(v301)
                end
            end
        end
    end)
    return u299
end)
u14:ConnectDataChanged(u46.MultiplierDataUpdates, function() --[[Function name: updateLikeBoard, line 1045]]
    --[[
    Upvalues:
        [1] = u14
        [2] = u46
        [3] = u302
        [4] = u297
        [5] = u29
        [6] = u5
    --]]
    if u14:Get() then
        for v303, v304 in u46.LikeBonusItems do
            local v305 = u302:Get()
            v305.LayoutOrder = v303
            v305.Parent = u297.Items
            u29:FormatAmount(v305.Amount, v304)
            v305:SetAttribute("Item", u5:JSONEncode(v304))
            u29:UpdateIcon(v305, v304)
        end
        u302:Done()
    end
end)
local u306 = v36.new(128, 2)
u306.Mode = "3D"
local u307 = u43.new():size(40)
local u308 = {}
local u309 = {}
local function v318(u310, u311) --[[Anonymous function at line 1081]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u10
        [3] = u56
        [4] = u41
        [5] = u43
        [6] = u27
        [7] = u26
        [8] = u42
        [9] = u40
        [10] = u25
        [11] = u12
        [12] = u31
        [13] = u306
    --]]
    local u312 = false
    u310.Spawn.Transparency = 1
    u310.Display.Transparency = 0.7
    u310.Display.CanCollide = false
    u310.Display.SurfaceGui.Container.Size = UDim2.new(1, -300, 1, -300)
    u310.Display.Touched:Connect(function(p313) --[[Anonymous function at line 1087]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u10
            [3] = u312
            [4] = u56
            [5] = u310
            [6] = u41
            [7] = u43
            [8] = u27
            [9] = u26
            [10] = u42
            [11] = u40
            [12] = u25
            [13] = u12
            [14] = u311
            [15] = u31
            [16] = u306
        --]]
        local v314 = p313.Parent
        if v314 then
            v314 = u2:FindFirstChild(p313.Parent.Name)
        end
        if v314 and (v314 == u10 and not u312) then
            if not u56:HasPlayer(v314) then
                u312 = true
                if not u310:GetAttribute("Unlocked") then
                    return u40(u41.new("Locked"):Text((("Discover %* to unlock this portal!"):format((u43.autoColor(u310.Name == "Event" and "Zen" or u310.Name, u27.Text.Yellow))))):Button(function(p315, p316) --[[Anonymous function at line 1098]]
                        --[[
                        Upvalues:
                            [1] = u26
                            [2] = u27
                            [3] = u42
                        --]]
                        p315.Label.Text = "Close"
                        u26(p315, u27.Button.Red)
                        u42(p315, p316)
                    end):OnClose(function() --[[Anonymous function at line 1103]]
                        --[[
                        Upvalues:
                            [1] = u312
                        --]]
                        task.delay(1, function() --[[Anonymous function at line 1104]]
                            --[[
                            Upvalues:
                                [1] = u312
                            --]]
                            u312 = false
                        end)
                    end):Build())
                end
                u25("Teleporting", function() --[[Anonymous function at line 1111]]
                    --[[
                    Upvalues:
                        [1] = u12
                        [2] = u311
                        [3] = u31
                        [4] = u10
                        [5] = u306
                        [6] = u312
                    --]]
                    u12:FireServer("Teleport", u311)
                    task.wait(0.5)
                    local v317 = u31:GetRootPart(u10)
                    if v317 then
                        u306:Update(v317.Position)
                    end
                    task.delay(1, function() --[[Anonymous function at line 1118]]
                        --[[
                        Upvalues:
                            [1] = u312
                        --]]
                        u312 = false
                    end)
                end)
            end
        else
            return
        end
    end)
    u306:Add(u310:GetPivot().Position, u310)
end
local function v327() --[[Anonymous function at line 1127]]
    --[[
    Upvalues:
        [1] = u14
        [2] = u308
        [3] = u309
        [4] = u10
        [5] = u307
        [6] = u43
        [7] = u27
    --]]
    local v319 = u14:Get()
    if v319 then
        for v320, v321 in u308 do
            local v322 = v321.Model.Name
            local v323 = v322 == "Event" and "Zen" or v322
            local v324 = v319.AreasUnlocked[v323] ~= nil
            if u309[v321.Model] then
                local v325 = v321.Locked
                local v326
                if v324 then
                    v326 = nil
                else
                    v326 = workspace.Rendered.Generic or nil
                end
                v325.Parent = v326
            end
            if u10:GetAttribute("InRace") then
                v321.Model.Display.Color = Color3.new()
                v321.Model.Display.SurfaceGui.Label.Text = ("%*\nRaces"):format((u307("Disable in")))
            else
                v321.Model.Display.Color = v324 and Color3.fromRGB(28, 51, 255) or Color3.fromRGB(177, 0, 0)
                v321.Model.Display.SurfaceGui.Label.Text = ("%*\n%*"):format(u307(v324 and "Teleport to" or "Discover"), (u43.autoColor(v320, u27.Text.Cyan)))
            end
            v321.Model.Display.SurfaceGui.Enabled = v324
            v321.Model:SetAttribute("Unlocked", v324)
        end
    end
end
for _, v328 in workspace.Worlds:GetChildren() do
    local v329 = v328:FindFirstChild("Portals")
    if v329 then
        for _, v330 in v329:GetChildren() do
            local v331 = u1.Assets.VisualPortalLock:Clone()
            v331:PivotTo(v330:GetPivot() - Vector3.new(0, 1, 0))
            v331.Lock.BillboardGui.Label.Text = ("%*\n%*"):format(u307("Discover"), (u43.autoColor(v330.Name == "Event" and "Zen" or v330.Name, u27.Text.Cyan)))
            u308[v330.Name] = {
                ["Model"] = v330,
                ["Locked"] = v331
            }
            if v330.Name == "Event" then
                v318(v330, workspace.Event.Portal.Spawn:GetFullName())
            else
                v318(v330, v54.toIslandLocation(v328.Name, v330.Name))
            end
        end
    end
    local v332 = v328:FindFirstChild("Islands")
    if v332 then
        for _, v333 in v332:GetChildren() do
            local v334 = v333.Island:FindFirstChild("Portal")
            local v335 = workspace.Worlds[v328.Name]:FindFirstChild("PortalSpawn")
            if v334 and v335 then
                v334:SetAttribute("Unlocked", true)
                v318(v334, v335:GetFullName())
            end
        end
    end
end
v318(workspace.Event.Portal, workspace.Worlds["The Overworld"].PortalSpawn:GetFullName())
u10:GetAttributeChangedSignal("InRace"):Connect(v327)
u14:ConnectDataChanged("AreasUnlocked", v327)
u306.Loaded:Connect(function(p336) --[[Anonymous function at line 1193]]
    --[[
    Upvalues:
        [1] = u14
        [2] = u308
        [3] = u309
    --]]
    local v337 = u14:Get()
    local v338 = u308[p336.Name]
    if v338 and v337 then
        local v339 = p336.Name
        local v340 = v339 == "Event" and "Zen" or v339
        local v341 = v337.AreasUnlocked[v340] ~= nil
        local v342 = v338.Locked
        local v343
        if v341 then
            v343 = nil
        else
            v343 = workspace.Rendered.Generic or nil
        end
        v342.Parent = v343
    end
    u309[p336] = true
    p336.Parent = workspace.Rendered.Generic
end)
u306.Unloaded:Connect(function(p344) --[[Anonymous function at line 1208]]
    --[[
    Upvalues:
        [1] = u308
        [2] = u309
    --]]
    local v345 = u308[p344.Name]
    if v345 then
        v345.Locked.Parent = nil
    end
    u309[p344] = nil
    p344.Parent = nil
end)
v15(2, function() --[[Anonymous function at line 1217]]
    --[[
    Upvalues:
        [1] = u31
        [2] = u10
        [3] = u306
    --]]
    local v346 = u31:GetRootPart(u10)
    if v346 then
        u306:Update(v346.Position)
    end
end)
local u347 = TweenInfo.new(2, Enum.EasingStyle.Linear)
local u348 = TweenInfo.new(0.25, Enum.EasingStyle.Sine)
local u349 = TweenInfo.new(0.65, Enum.EasingStyle.Sine)
local u350 = Instance.new("Frame")
u350.BackgroundTransparency = 1
u350.AnchorPoint = Vector2.one / 2
u350.Size = UDim2.fromScale(1, 1)
u350.Position = UDim2.fromScale(0.5, 0.5)
local v351 = Instance.new("UIStroke")
v351.Color = Color3.fromRGB(39, 97, 255)
v351.Thickness = 5
v351.Parent = u350
v15(0.4, function() --[[Anonymous function at line 1239]]
    --[[
    Upvalues:
        [1] = u55
        [2] = u309
        [3] = u350
        [4] = u4
        [5] = u348
        [6] = u347
        [7] = u349
    --]]
    if not u55.Enabled then
        for v352 in u309 do
            if v352:GetAttribute("Unlocked") then
                local u353 = u350:Clone()
                u353.UIStroke.Transparency = 1
                u353.Parent = v352.Display.SurfaceGui.Container
                u4:Create(u353.UIStroke, u348, {
                    ["Transparency"] = 0.65
                }):Play()
                u4:Create(u353.UIStroke, u347, {
                    ["Color"] = Color3.fromRGB(124, 248, 255)
                }):Play()
                u4:Create(u353, u347, {
                    ["Size"] = UDim2.new(1, 300, 1, 300)
                }):Play()
                task.delay(u347.Time - u349.Time, function() --[[Anonymous function at line 1253]]
                    --[[
                    Upvalues:
                        [1] = u4
                        [2] = u353
                        [3] = u349
                    --]]
                    u4:Create(u353.UIStroke, u349, {
                        ["Transparency"] = 1
                    }):Play()
                end)
                task.delay(u347.Time, function() --[[Anonymous function at line 1256]]
                    --[[
                    Upvalues:
                        [1] = u353
                    --]]
                    u353:Destroy()
                end)
            end
        end
    end
end)
local u354 = workspace.Worlds["The Overworld"].HatchingZoneBarrier
local u355 = workspace.Worlds["The Overworld"].HatchingZone
local u356 = u355:GetPivot()
local u357 = u354.Display.SurfaceGui
u357.Adornee = u354.Display
u357.Requirement.Cost.Label.Text = u16(u29:GetAmount(u46.HatchingZoneCost))
u357.Parent = u28()
task.defer(function() --[[Anonymous function at line 1274]]
    --[[
    Upvalues:
        [1] = u357
    --]]
    u357.Requirement.Cost.Label.Size = UDim2.fromOffset(u357.Requirement.Cost.Label.TextBounds.X + 50, 50)
end)
local u358 = nil
local function v363() --[[Anonymous function at line 1279]]
    --[[
    Upvalues:
        [1] = u14
        [2] = u358
        [3] = u24
        [4] = u355
        [5] = u354
        [6] = u356
    --]]
    local v359 = u14:Get()
    if v359 then
        local v360 = v359.AreasUnlocked["Hatching Zone"] ~= nil
        if v360 ~= u358 then
            if u358 ~= nil then
                u24:Beam(u355)
            end
            local v361 = u354
            local v362
            if v360 then
                v362 = nil
            else
                v362 = workspace.Rendered.Generic or nil
            end
            v361.Parent = v362
            u355:PivotTo(u356 + Vector3.new(0, v360 and -1000 or 0, 0))
            u358 = v360
        end
    else
        return
    end
end
u24:Bind(u355, function() --[[Anonymous function at line 1296]]
    --[[
    Upvalues:
        [1] = u52
        [2] = u46
        [3] = u14
        [4] = u29
        [5] = u41
        [6] = u40
        [7] = u24
        [8] = u355
        [9] = u12
        [10] = u43
        [11] = u27
        [12] = u53
    --]]
    u53(u52.new("Hatching Zone"):Cost(u46.HatchingZoneCost, function() --[[Anonymous function at line 1298]]
        --[[
        Upvalues:
            [1] = u14
            [2] = u29
            [3] = u46
            [4] = u41
            [5] = u40
            [6] = u24
            [7] = u355
            [8] = u12
        --]]
        local v364 = u14:Get()
        if v364 then
            if u29:GetOwnedAmount(v364, u46.HatchingZoneCost) < u29:GetAmount(u46.HatchingZoneCost) then
                return u40(u41.fromNotEnough(v364, u46.HatchingZoneCost, function(p365) --[[Anonymous function at line 1303]]
                    return ("You need %* to unlock the Hatching Zone!"):format(p365)
                end):Build())
            end
            u24:Beam(u355)
            u12:FireServer("UnlockHatchingZone")
        end
    end):Unlock("Easy Hatch", "rbxassetid://79347391641627", (("Access %* you\'ve unlocked in one spot!"):format((u43.autoColor("every egg", u27.Text.Cyan))))):Unlock("Infinity Egg", u46.InfinityEgg, (("Hatch pets from %* at the same time!"):format((u43.autoColor("multiple eggs", u27.Text.Pink))))):Unlock("COMING SOON", "rbxassetid://105147490567221", (("A dedicated %* to post trades, search for pets, and make deals with other users!"):format((u43.autoColor("trading server", u27.Text.Yellow))))):Build())
end)
u14:ConnectDataChanged("AreasUnlocked", v363)
local u366 = workspace.Worlds["The Overworld"].VIPArea.VIPBarrier
u14:ConnectDataChanged("Passes", function(p367) --[[Anonymous function at line 1327]]
    --[[
    Upvalues:
        [1] = u366
    --]]
    local v368 = u366
    local v369
    if p367.Passes.VIP then
        v369 = nil
    else
        v369 = workspace.Rendered.Generic or nil
    end
    v368.Parent = v369
end)
for _, v370 in workspace.Rendered.Chests:GetChildren() do
    v370.Transparency = 1
    u39.Chest(v370.Name, v370)
end
u12.Event("RenderGifts"):Connect(function(p371) --[[Anonymous function at line 1339]]
    --[[
    Upvalues:
        [1] = u39
    --]]
    for v372, v373 in p371 do
        u39.Gift(v372, v373.Name, v373.Position)
    end
end)
u24:Bind(workspace.Worlds["The Overworld"].Islands["Outer Space"].Island.Mastery, function() --[[Anonymous function at line 1350]]
    --[[
    Upvalues:
        [1] = u17
    --]]
    u17:Open("Mastery")
end)
local u374 = require(u1.Client.Effects.Animated.Atom)
local u375 = require(u1.Client.Effects.Animated.Enchanter)
local u376 = workspace.Worlds["The Overworld"].Islands["Outer Space"].Island.Atom.CFrame
v49(u376.Position, function() --[[Anonymous function at line 1361]]
    --[[
    Upvalues:
        [1] = u374
        [2] = u376
    --]]
    return u374(u376, Color3.fromRGB(26, 121, 156))
end)
local v377 = workspace.Worlds["The Overworld"].Islands["The Void"].Island
local u378 = v377.EnchanterRoot.CFrame
v49(u378.Position, function() --[[Anonymous function at line 1369]]
    --[[
    Upvalues:
        [1] = u375
        [2] = u378
    --]]
    return u375(u378)
end)
u24:Bind(v377.EnchanterActivation, function() --[[Anonymous function at line 1373]]
    --[[
    Upvalues:
        [1] = u59
    --]]
    u59:PromptSelectPet()
end)
local u379 = workspace.Worlds["The Overworld"].FastTravel
local v380 = workspace.Worlds["The Overworld"].Balloon
local u381 = v380.Inactive
local u382 = v380.Active
u14:ConnectDataChanged({ "AreasUnlocked", "MasteryUpgrades" }, function() --[[Function name: update, line 1388]]
    --[[
    Upvalues:
        [1] = u14
        [2] = u381
        [3] = u382
        [4] = u379
        [5] = u72
    --]]
    local v383 = u14:Get()
    local v384 = false
    if v383 then
        for _, v385 in v383.MasteryUpgrades do
            if v385.Type == "WorldMap" then
                v384 = true
                break
            end
        end
    end
    local v386 = u381
    local v387
    if v384 then
        v387 = nil
    else
        v387 = workspace.Rendered or nil
    end
    v386.Parent = v387
    u382.Parent = v384 and workspace.Rendered or nil
    u379.Parent = v384 and workspace.Worlds["The Overworld"] or nil
    u72.HUD.Left.Buttons.Map.Visible = v384
    u72.HUD.Left.Buttons.Mastery.Visible = v383 and v383.AreasUnlocked["Outer Space"] ~= nil and true or false
end)
local u388 = {}
local u389 = u72.HUD.LootPoolViewer
local u390 = false
local function u399(u391) --[[Anonymous function at line 1432]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u4
        [3] = u390
        [4] = u33
        [5] = u1
        [6] = u63
        [7] = u37
    --]]
    local u392 = Instance.new("CFrameValue")
    local u393 = u391.Decoration.Top:GetPivot()
    local v394 = u3.Heartbeat:Connect(function() --[[Anonymous function at line 1436]]
        --[[
        Upvalues:
            [1] = u391
            [2] = u393
            [3] = u392
        --]]
        if u391.Parent then
            u391.Decoration.Top:PivotTo(u393 * u392.Value)
        end
    end)
    u4:Create(u392, TweenInfo.new(0.45, Enum.EasingStyle.Back), {
        ["Value"] = CFrame.Angles(1.9198621771937625, 0, 0)
    }):Play()
    u390 = true
    u33:Attach(nil)
    local v395 = Instance.new("Attachment")
    v395.WorldPosition = u391.Inner.Position - Vector3.new(0, 4, 0)
    v395.Parent = workspace.Terrain
    for _, v396 in u1.Assets.Particles.Pillar:GetChildren() do
        local v397 = v396:Clone()
        v397.Size = u63.transformNumberSequence(v397.Size, function(p398) --[[Anonymous function at line 1455]]
            return NumberSequenceKeypoint.new(p398.Time, p398.Value * 2.5)
        end)
        v397.Enabled = false
        v397.Parent = v395
        v397:Emit(15)
    end
    u37("ChestCollect", u391.Inner.Position)
    task.wait(2)
    u4:Create(u392, TweenInfo.new(0.5, Enum.EasingStyle.Sine), {
        ["Value"] = CFrame.new()
    }):Play()
    task.wait(0.5)
    u392:Destroy()
    v395:Destroy()
    v394:Disconnect()
    task.wait(0.15)
    u390 = false
end
local function u408(p400) --[[Anonymous function at line 1478]]
    --[[
    Upvalues:
        [1] = u388
        [2] = u4
        [3] = u65
        [4] = u66
    --]]
    local v401 = p400.Visual:Clone()
    v401.Mesh.TextureId = ""
    v401.Transparency = 0
    v401.CanCollide = false
    v401.Material = Enum.Material.Neon
    v401.Color = Color3.fromRGB(255, 255, 255)
    v401.Parent = workspace.Rendered.Generic
    p400.Visual.Transparency = 1
    p400.Visual.CanCollide = false
    local v402 = p400.Prompt:FindFirstChildOfClass("ProximityPrompt")
    if v402 then
        v402.Enabled = false
    end
    u388[p400.Prompt] = nil
    local v403 = TweenInfo.new(1.25, Enum.EasingStyle.Quint)
    u4:Create(v401, v403, {
        ["Transparency"] = 1
    }):Play()
    u4:Create(v401.Mesh, v403, {
        ["Offset"] = v401.Mesh.Offset * 2,
        ["Scale"] = v401.Mesh.Scale * 2
    }):Play()
    local v404 = task.spawn
    local v405 = u65
    local v406 = v401.Position
    local v407 = v401.Size.Y / 2
    v404(v405, v406 - Vector3.new(0, v407, 0), 8)
    task.spawn(u66, v401.Position)
    task.wait(v403.Time)
    v401:Destroy()
end
local function u438(u409) --[[Anonymous function at line 1510]]
    --[[
    Upvalues:
        [1] = u61
        [2] = u18
        [3] = u389
        [4] = u390
        [5] = u388
        [6] = u33
        [7] = u14
        [8] = u62
        [9] = u29
        [10] = u41
        [11] = u40
        [12] = u399
        [13] = u12
        [14] = u50
        [15] = u408
    --]]
    local v410 = u409:GetAttribute("DespawnAt")
    if not v410 then
        return
    end
    local v411 = u409:GetAttribute("Type")
    if v411 ~= "Egg" then
        if v411 == "Chest" then
            local v412 = u409:FindFirstChild("Display")
            if v412 then
                local u413 = u409:FindFirstChild("Chest")
                if u413 then
                    local u414 = u413:FindFirstChild("Prompt")
                    if u414 then
                        u414.Name = u409.Name
                        local u417 = u389:GetPropertyChangedSignal("Visible"):Connect(function() --[[Anonymous function at line 1564]]
                            --[[
                            Upvalues:
                                [1] = u413
                                [2] = u389
                                [3] = u390
                            --]]
                            local v415 = u413.Inner.RiftChest
                            local v416 = not u389.Visible
                            if v416 then
                                v416 = not u390
                            end
                            v415.Enabled = v416
                        end)
                        local u418 = u61(v412.SurfaceGui.Timer, v410, true)
                        local function v419() --[[Anonymous function at line 1569]]
                            --[[
                            Upvalues:
                                [1] = u388
                                [2] = u414
                                [3] = u418
                                [4] = u417
                            --]]
                            u388[u414] = nil
                            u418:Disconnect()
                            u417:Disconnect()
                        end
                        u388[u414] = function() --[[Function name: buildPrompt, line 1575]]
                            --[[
                            Upvalues:
                                [1] = u33
                                [2] = u409
                                [3] = u414
                                [4] = u14
                                [5] = u62
                                [6] = u29
                                [7] = u41
                                [8] = u40
                                [9] = u399
                                [10] = u413
                                [11] = u12
                            --]]
                            u33.fromRiftChest(u409.Name)
                            u33:Attach(u414)
                            u33.Activated:Connect(function(p420) --[[Anonymous function at line 1578]]
                                --[[
                                Upvalues:
                                    [1] = u14
                                    [2] = u62
                                    [3] = u409
                                    [4] = u29
                                    [5] = u41
                                    [6] = u40
                                    [7] = u399
                                    [8] = u413
                                    [9] = u12
                                --]]
                                local v421 = u14:Get()
                                if v421 then
                                    local v422 = u62[u409.Name]
                                    if v422.Type == "Chest" then
                                        if u29:GetOwnedAmount(v421, v422.Cost) < u29:GetAmount(v422.Cost) then
                                            return u40(u41.fromNotEnough(v421, v422.Cost, function(p423) --[[Anonymous function at line 1588]]
                                                return ("You need %* to unlock this chest."):format(p423)
                                            end):Build())
                                        end
                                        task.spawn(u399, u413)
                                        u12:FireServer("UnlockRiftChest", u409.Name, p420 == 3)
                                    end
                                else
                                    return
                                end
                            end)
                        end
                        return v419
                    end
                end
            else
                return
            end
        elseif v411 == "Gift" then
            local v424 = u409:FindFirstChild("Display")
            if v424 then
                local u425 = u409:FindFirstChild("Gift")
                if u425 then
                    local u426 = u425.Prompt
                    local u427 = u61(v424.SurfaceGui.Timer, v410, true)
                    local function v428() --[[Anonymous function at line 1615]]
                        --[[
                        Upvalues:
                            [1] = u388
                            [2] = u426
                            [3] = u427
                        --]]
                        u388[u426] = nil
                        u427:Disconnect()
                    end
                    u388[u426] = function() --[[Function name: buildPrompt, line 1620]]
                        --[[
                        Upvalues:
                            [1] = u50
                            [2] = u425
                            [3] = u408
                            [4] = u12
                            [5] = u409
                        --]]
                        local u429 = u50.new()
                        u429.Parent = u425.Prompt
                        u429.ActionText = "Open!"
                        u429.MaxActivationDistance = 10
                        u429.Triggered:Connect(function() --[[Anonymous function at line 1625]]
                            --[[
                            Upvalues:
                                [1] = u429
                                [2] = u408
                                [3] = u425
                                [4] = u12
                                [5] = u409
                            --]]
                            u429.Enabled = false
                            task.spawn(u408, u425)
                            u12:FireServer("ClaimRiftGift", u409.Name)
                        end)
                    end
                    return v428
                end
            end
        elseif v411 == "Shop" then
            local v430 = u409:FindFirstChild("Display")
            if v430 then
                local u431 = u61(v430.SurfaceGui.Timer, v410, true)
                return function() --[[Function name: cleanup, line 1642]]
                    --[[
                    Upvalues:
                        [1] = u431
                    --]]
                    u431:Disconnect()
                end
            end
        end
    end
    local v432 = u409:FindFirstChild("Display")
    if not v432 then
        return
    end
    local u433 = v432.SurfaceGui.Icon:FindFirstChild("Luck")
    if not u433 then
        return
    end
    local u434 = u61(v432.SurfaceGui.Timer, v410, true)
    local function v435() --[[Anonymous function at line 1530]]
        --[[
        Upvalues:
            [1] = u434
            [2] = u18
            [3] = u433
        --]]
        u434:Disconnect()
        u18.None(u433)
    end
    for _, v436 in script:GetChildren() do
        if v436:HasTag("Egg") then
            local v437 = v436:GetAttribute("BonusLuck") or 1
            u433.Text = tostring(v437)
            break
        end
    end
    u18.Cycle(u433, "Rainbow", {
        ["Speed"] = 0.75
    })
    return v435
end
local u439 = u390
for _, v440 in workspace.Rendered.Rifts:GetChildren() do
    local v441 = u438(v440)
    if v441 then
        v440.Destroying:Connect(v441)
    end
end
local u442 = nil
v15(0.25, function() --[[Anonymous function at line 1659]]
    --[[
    Upvalues:
        [1] = u31
        [2] = u10
        [3] = u439
        [4] = u388
        [5] = u442
        [6] = u33
    --]]
    local v443 = u31:GetRootPart(u10)
    if v443 then
        local v444 = nil
        if not u439 then
            local v445 = (1 / 0)
            for v446, _ in u388 do
                local v447 = (v446.Position - v443.Position).Magnitude
                if v447 < v445 and v447 < 10 then
                    v444 = v446
                    v445 = v447
                end
            end
        end
        if v444 ~= u442 then
            if v444 then
                u388[v444]()
            else
                u33:Attach(nil)
            end
            u442 = v444
        end
    end
end)
u14:ConnectDataChanged("Powerups", function() --[[Anonymous function at line 1687]]
    --[[
    Upvalues:
        [1] = u442
        [2] = u388
    --]]
    if u442 then
        u388[u442]()
    end
end)
workspace.Rendered.Rifts.ChildAdded:Connect(function(u448) --[[Anonymous function at line 1693]]
    --[[
    Upvalues:
        [1] = u438
    --]]
    task.wait(5)
    task.defer(function() --[[Anonymous function at line 1695]]
        --[[
        Upvalues:
            [1] = u438
            [2] = u448
        --]]
        local v449 = u438(u448)
        if v449 then
            u448.Destroying:Connect(v449)
        end
    end)
end)
local v450 = u46.ShopTag
local u451 = u46.ShopIdAttribute
local function v454(p452) --[[Anonymous function at line 1711]]
    --[[
    Upvalues:
        [1] = u451
        [2] = u24
        [3] = u64
        [4] = u17
    --]]
    local u453 = p452:GetAttribute(u451)
    if u453 then
        u24:Bind(p452, function() --[[Anonymous function at line 1714]]
            --[[
            Upvalues:
                [1] = u64
                [2] = u453
                [3] = u17
            --]]
            u64:SetShopId(u453)
            u17:Open("ItemShop")
        end)
    end
end
local function v457(p455) --[[Anonymous function at line 1721]]
    --[[
    Upvalues:
        [1] = u451
        [2] = u17
        [3] = u64
        [4] = u24
    --]]
    local v456 = p455:GetAttribute(u451)
    if v456 and (u17:IsOpen("ItemShop") and u64:GetShopId() == v456) then
        u17:Close()
    end
    u24:Remove(p455)
end
for _, v458 in u8:GetTagged(v450) do
    local u459 = v458:GetAttribute(u451)
    if u459 then
        u24:Bind(v458, function() --[[Anonymous function at line 1714]]
            --[[
            Upvalues:
                [1] = u64
                [2] = u459
                [3] = u17
            --]]
            u64:SetShopId(u459)
            u17:Open("ItemShop")
        end)
    end
end
u8:GetInstanceAddedSignal(v450):Connect(v454)
u8:GetInstanceRemovedSignal(v450):Connect(v457)
for _, v460 in u8:GetTagged("NPC") do
    v60(v460)
end
for _, v461 in u1.Client.Gui.Frames:GetChildren() do
    require(v461)
end
u24.Changed:Connect(function(p462) --[[Anonymous function at line 1746]]
    --[[
    Upvalues:
        [1] = u17
    --]]
    if not p462 and u17:IsAnyOpen() then
        u17:Close()
    end
end)
u17:BindOnOpened("any", function() --[[Anonymous function at line 1752]]
    --[[
    Upvalues:
        [1] = u19
    --]]
    u19:Hide()
end)
u17:BindOnClosed("any", function() --[[Anonymous function at line 1756]]
    --[[
    Upvalues:
        [1] = u19
    --]]
    u19:Hide()
end)
u22:SetVisible(true)
v13(u72.UIScale)
require(u1.Client.Gui.HUD)
require(u1.Shared.GlobalEvent)
task.spawn(function() --[[Anonymous function at line 1777]]
    --[[
    Upvalues:
        [1] = u14
        [2] = u67
        [3] = u3
        [4] = u17
        [5] = u35
    --]]
    while not u14:IsReady() do
        task.wait(0.5)
    end
    task.wait(1)
    local v463 = u14:Get()
    if v463 then
        if u67.Active then
            return
        elseif u3:IsStudio() then
            return
        elseif v463.LastUpdate.ShouldDisplay then
            u17:Open("_changelog")
        else
            local v464 = v463.DailyRewards
            if u35.getAbsoluteDay() <= v464.LastDay and v464.Claimed == false then
                u17:Open("DailyRewards")
            end
        end
    else
        return
    end
end)

-- Script Path: game:GetService("Players").LocalPlayer.PlayerScripts.ClientScript