local u1 = game:GetService("Players")
local v2 = game:GetService("ReplicatedStorage")
local u3 = game:GetService("HttpService")
game:GetService("LocalizationService")
local u4 = game:GetService("TweenService")
local v5 = game:GetService("Workspace")
local u6 = require(v2.Client.Gui.Utils.AutoButtonColor)
local u7 = require(v2.Shared.Palette)
local u8 = require(v2.Client.Gui.Utils.ClickableButton)
local u9 = require(v2.Client.Gui.GuiFrame)
local u10 = require(v2.Shared.Data.CompetitiveShared)
local u11 = require(v2.Client.Framework.Services.LocalData)
local v12 = require(v2.Client.Gui.Utils.ItemFrame)
local u13 = require(v2.Shared.Framework.Utilities.String.FormatSuffix)
local u14 = require(v2.Client.Framework.Utilities.Gui.Animations.SetFade)
local v15 = require(v2.Client.Framework.Utilities.Gui.HookScrollingFrame)
local u16 = require(v2.Shared.Data.Powerups)
local u17 = require(v2.Shared.Utils.Stats.ItemUtil)
local u18 = require(v2.Shared.Framework.Utilities.Math.Time)
require(v2.Client.Gui.Utils.UpdateWhileGuiOpen)
local u19 = require(v2.Shared.Constants)
local u20 = require(v2.Shared.Framework.Utilities.String.FormatCommas)
local u21 = require(v2.Shared.Framework.Network.Remote)
require(v2.Shared.Types)
local v22 = require(v2.Shared.Framework.Classes.Queue)
local u23 = require(v2.Shared.Utils.RichText)
local u24 = require(v2.Client.Effects.OutlinePulse)
local u25 = require(v2.Shared.Data.Builders.PromptBuilder)
local u26 = require(v2.Client.Gui.Prompt)
local v27 = require(v2.Shared.Framework.Classes.Pool)
local u28 = require(v2.Shared.Utils.Stats.QuestUtil)
local u29 = require(v2.Client.Gui.Utils.PlayLocalSound)
local u30 = require(v2.Shared.Framework.Utilities.String.FormatOrdinal)
local u31 = u1.LocalPlayer
local u32 = u31.PlayerGui.ScreenGui
local u33 = u32.Competitive.Frame
local u34 = v5.Worlds["The Overworld"].Leaderboards.Competitive
local v35 = u33.Content.Rewards.Template
v35.Parent = nil
local u36 = u33.Content.Tasks.Template
u36.Parent = nil
local u40 = v27.new(function() --[[Anonymous function at line 54]]
    --[[
    Upvalues:
        [1] = u36
        [2] = u6
        [3] = u7
        [4] = u8
        [5] = u11
        [6] = u17
        [7] = u10
        [8] = u25
        [9] = u26
        [10] = u29
        [11] = u21
    --]]
    local u37 = u36:Clone()
    u6(u37.Content.Reroll.Button, u7.Button.Orange)
    u8(u37.Content.Reroll.Button, function() --[[Anonymous function at line 58]]
        --[[
        Upvalues:
            [1] = u11
            [2] = u17
            [3] = u10
            [4] = u25
            [5] = u26
            [6] = u29
            [7] = u21
            [8] = u37
        --]]
        local v38 = u11:Get()
        if v38 then
            if u17:GetOwnedAmount(v38, u10.RerollCost) < u17:GetAmount(u10.RerollCost) then
                return u26(u25.fromNotEnough(v38, u10.RerollCost, function(p39) --[[Anonymous function at line 63]]
                    return ("You need %* to reroll this task!"):format(p39)
                end):Build())
            end
            u29("Reroll")
            u21:FireServer("CompetitiveReroll", u37.LayoutOrder)
        end
    end)
    return u37
end)
u21.Event("ClanNotify"):Connect(function(p41) --[[Anonymous function at line 95]]
    --[[
    Upvalues:
        [1] = u25
        [2] = u6
        [3] = u7
        [4] = u8
        [5] = u26
    --]]
    return u26(u25.new("Notification"):Text(p41):Button(function(p42, p43) --[[Anonymous function at line 98]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u7
            [3] = u8
        --]]
        p42.Label.Text = "Okay"
        u6(p42, u7.Button.Purple)
        u8(p42, p43)
    end):Build())
end)
local u44 = u33.Content.Team
local u45 = u44.Content
local u46 = u45.Members
local v47 = u46.Template
v47.Parent = nil
local v48 = u46.TemplateEmpty
v48.Parent = nil
local u49 = {}
local u50 = {}
local u51 = {}
for u52 = 1, u19.Clan.MaxMembers do
    local v53 = v47:Clone()
    v53.Main.Owner.Visible = u52 == 1
    v53.LayoutOrder = u52
    v53.Visible = false
    u6(v53.Kick.Button, u7.Button.Red)
    u8(v53.Kick.Button, function() --[[Anonymous function at line 134]]
        --[[
        Upvalues:
            [1] = u11
            [2] = u52
            [3] = u25
            [4] = u23
            [5] = u7
            [6] = u6
            [7] = u8
            [8] = u21
            [9] = u26
        --]]
        local v54 = u11:Get()
        if v54 then
            local v55
            if v54.Competitive then
                v55 = v54.Competitive.Clan
            else
                v55 = nil
            end
            if v55 then
                local u56 = v55.Members[u52]
                if u56 then
                    u26(u25.new("Kick User"):Text((("Are you sure you want to kick %*?"):format((u23.autoColor(u56.DisplayName, u7.Text.Cyan))))):Button(function(p57, u58) --[[Anonymous function at line 152]]
                        --[[
                        Upvalues:
                            [1] = u6
                            [2] = u7
                            [3] = u8
                            [4] = u21
                            [5] = u56
                        --]]
                        p57.Label.Text = "Yes"
                        u6(p57, u7.Button.Green)
                        u8(p57, function() --[[Anonymous function at line 155]]
                            --[[
                            Upvalues:
                                [1] = u21
                                [2] = u56
                                [3] = u58
                            --]]
                            u21:FireServer("ClanKickMember", u56.UserId)
                            u58()
                        end)
                    end):Button(function(p59, p60) --[[Anonymous function at line 160]]
                        --[[
                        Upvalues:
                            [1] = u6
                            [2] = u7
                            [3] = u8
                        --]]
                        p59.Label.Text = "No"
                        u6(p59, u7.Button.Red)
                        u8(p59, p60)
                    end):Build())
                end
            else
                return
            end
        else
            return
        end
    end)
    v53.Parent = u46
    u49[u52] = v53
end
for v61 = 1, u19.Clan.MaxMembers do
    local v62 = v48:Clone()
    v62.LayoutOrder = v61
    v62.Visible = false
    v62.Parent = u46
    u50[v61] = v62
end
u6(u46.Invite.Button, u7.Button.Default)
u8(u46.Invite.Button, function() --[[Anonymous function at line 183]]
    --[[
    Upvalues:
        [1] = u9
    --]]
    u9:Open("CompetitiveInvite")
end)
u6(u45.TrayButtons.Leave.Button, u7.Button.Red)
u8(u45.TrayButtons.Leave.Button, function() --[[Anonymous function at line 189]]
    --[[
    Upvalues:
        [1] = u25
        [2] = u6
        [3] = u7
        [4] = u8
        [5] = u21
        [6] = u26
    --]]
    u26(u25.new("Leave Team"):Text("Are you sure you want to leave this Team? You can only leave and join a team once every 12 hours."):Button(function(p63, u64) --[[Anonymous function at line 192]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u7
            [3] = u8
            [4] = u21
        --]]
        p63.Label.Text = "Yes"
        u6(p63, u7.Button.Green)
        u8(p63, function() --[[Anonymous function at line 195]]
            --[[
            Upvalues:
                [1] = u21
                [2] = u64
            --]]
            u21:FireServer("ClanLeave")
            u64()
        end)
    end):Button(function(p65, p66) --[[Anonymous function at line 200]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u7
            [3] = u8
        --]]
        p65.Label.Text = "No"
        u6(p65, u7.Button.Red)
        u8(p65, p66)
    end):Build())
end)
u6(u45.TrayButtons.Delete.Button, u7.Button.Red)
u8(u45.TrayButtons.Delete.Button, function() --[[Anonymous function at line 211]]
    --[[
    Upvalues:
        [1] = u25
        [2] = u6
        [3] = u7
        [4] = u8
        [5] = u21
        [6] = u26
    --]]
    u26(u25.new("Delete Team"):Text("Are you sure you want to delete this Team? You can only create and delete a team once every 12 hours. This action is permanent!"):Button(function(p67, u68) --[[Anonymous function at line 214]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u7
            [3] = u8
            [4] = u21
        --]]
        p67.Label.Text = "Yes"
        u6(p67, u7.Button.Green)
        u8(p67, function() --[[Anonymous function at line 217]]
            --[[
            Upvalues:
                [1] = u21
                [2] = u68
            --]]
            u21:FireServer("ClanDelete")
            u68()
        end)
    end):Button(function(p69, p70) --[[Anonymous function at line 222]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u7
            [3] = u8
        --]]
        p69.Label.Text = "No"
        u6(p69, u7.Button.Red)
        u8(p69, p70)
    end):Build())
end)
local v71 = u32.CompetitiveInvite
local u72 = v71.Frame.Inner.Frame.List
local u73 = u72.Template
u73.Parent = nil
local u74 = {}
u6(v71.Frame.Top.Close.Button, u7.Button.Red)
u8(v71.Frame.Top.Close.Button, function() --[[Anonymous function at line 243]]
    --[[
    Upvalues:
        [1] = u9
    --]]
    u9:Open("Competitive")
end)
u9:BindOnOpened("CompetitiveInvite", function() --[[Anonymous function at line 248]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u74
        [3] = u1
        [4] = u31
        [5] = u73
        [6] = u8
        [7] = u21
        [8] = u9
        [9] = u72
    --]]
    local v75 = u11:Get()
    if v75 and (v75.Competitive and v75.Competitive.Clan) then
        for _, v76 in u74 do
            v76:Destroy()
        end
        table.clear(u74)
        local v77 = {}
        for _, v78 in v75.Competitive.Clan.Members do
            local v79 = u1:GetPlayerByUserId(v78.UserId)
            if v79 ~= nil then
                table.insert(v77, v79)
            end
        end
        for v80, u81 in u1:GetPlayers() do
            if u81 ~= u31 and (not table.find(v77, u81) and (u81:GetAttribute("DataLoaded") and u81:GetAttribute("HasClan") ~= true)) then
                local v82 = u73:Clone()
                v82.Name = ("Entry%*"):format(v80)
                v82.LayoutOrder = v80
                v82.Button.DisplayName.Text = u81.DisplayName
                v82.Button.Username.Text = ("@%*"):format(u81.Name)
                v82.Button.Icon.Image = ("rbxthumb://type=AvatarHeadShot&id=%*&w=100&h=100"):format(u81.UserId)
                u8(v82.Button, function() --[[Anonymous function at line 280]]
                    --[[
                    Upvalues:
                        [1] = u21
                        [2] = u81
                        [3] = u9
                    --]]
                    u21:FireServer("ClanSendInvite", u81)
                    u9:Open("Competitive")
                end)
                v82.Parent = u72
                local v83 = u74
                table.insert(v83, v82)
            end
        end
    end
end)
v15(u72, u72.UIGridLayout)
local u84 = TweenInfo.new(0.35, Enum.EasingStyle.Sine)
local u85 = TweenInfo.new(u84.Time, Enum.EasingStyle.Back, Enum.EasingDirection.In)
local u86 = v22.new()
local u87 = u32.HUD.CompetitiveInvitePopup
u87.Visible = true
u87.Parent = nil
local function u100(u88, u89) --[[Anonymous function at line 310]]
    --[[
    Upvalues:
        [1] = u86
        [2] = u87
        [3] = u23
        [4] = u7
        [5] = u32
        [6] = u6
        [7] = u8
        [8] = u25
        [9] = u21
        [10] = u26
        [11] = u4
        [12] = u84
        [13] = u24
        [14] = u85
    --]]
    u86:Push(5, function() --[[Anonymous function at line 311]]
        --[[
        Upvalues:
            [1] = u87
            [2] = u23
            [3] = u88
            [4] = u7
            [5] = u32
            [6] = u6
            [7] = u8
            [8] = u25
            [9] = u21
            [10] = u89
            [11] = u26
            [12] = u86
            [13] = u4
            [14] = u84
            [15] = u24
            [16] = u85
        --]]
        local u90 = u87:Clone()
        u90.Position = UDim2.new(0.5, 0, 0, -10)
        u90.AnchorPoint = Vector2.new(0.5, 1)
        u90.Label.Text = ("%* invited you to their Team!"):format((u23.autoColor(u88.Name, u7.Text.Cyan)))
        u90.Parent = u32.HUD
        local u91 = false
        local function v92() --[[Anonymous function at line 319]]
            --[[
            Upvalues:
                [1] = u91
                [2] = u90
            --]]
            if not u91 then
                u91 = true
                u90:Destroy()
            end
        end
        local u93 = ("%* (@%*)"):format(u88.DisplayName, u88.Name)
        u6(u90.Buttons.Accept.Button, u7.Button.Green)
        u8(u90.Buttons.Accept.Button, function() --[[Anonymous function at line 330]]
            --[[
            Upvalues:
                [1] = u25
                [2] = u23
                [3] = u93
                [4] = u7
                [5] = u6
                [6] = u8
                [7] = u21
                [8] = u89
                [9] = u26
                [10] = u91
                [11] = u90
                [12] = u86
            --]]
            u26(u25.new("Join Team"):Text(("Are you sure you want to join %*\'s Team? "):format((u23.autoColor(u93, u7.Text.Cyan))) .. "You can only join a Team once every 12 hours."):Button(function(p94, u95) --[[Anonymous function at line 337]]
                --[[
                Upvalues:
                    [1] = u6
                    [2] = u7
                    [3] = u8
                    [4] = u21
                    [5] = u89
                --]]
                p94.Label.Text = "Yes"
                u6(p94, u7.Button.Green)
                u8(p94, function() --[[Anonymous function at line 340]]
                    --[[
                    Upvalues:
                        [1] = u21
                        [2] = u89
                        [3] = u95
                    --]]
                    u21:FireServer("ClanAcceptInvite", u89.Id)
                    u95()
                end)
            end):Button(function(p96, p97) --[[Anonymous function at line 345]]
                --[[
                Upvalues:
                    [1] = u6
                    [2] = u7
                    [3] = u8
                --]]
                p96.Label.Text = "No"
                u6(p96, u7.Button.Red)
                u8(p96, p97)
            end):Build())
            if not u91 then
                u91 = true
                u90:Destroy()
            end
            u86:Clear()
        end)
        u6(u90.Buttons.Decline.Button, u7.Button.Red)
        u8(u90.Buttons.Decline.Button, function() --[[Anonymous function at line 358]]
            --[[
            Upvalues:
                [1] = u91
                [2] = u90
                [3] = u86
            --]]
            if not u91 then
                u91 = true
                u90:Destroy()
            end
            u86:Next()
        end)
        u4:Create(u90, u84, {
            ["Position"] = UDim2.new(0.5, 0, 0, 40),
            ["AnchorPoint"] = Vector2.new(0.5, 0)
        }):Play()
        task.spawn(function() --[[Anonymous function at line 370]]
            --[[
            Upvalues:
                [1] = u88
                [2] = u90
            --]]
            local v98 = ("rbxthumb://type=AvatarHeadShot&id=%*&w=100&h=100"):format(u88.UserId)
            u90.Icon.Label.Image = v98
        end)
        task.delay(u84.Time, function() --[[Anonymous function at line 376]]
            --[[
            Upvalues:
                [1] = u91
                [2] = u24
                [3] = u90
            --]]
            if not u91 then
                u24(function(p99) --[[Anonymous function at line 378]]
                    --[[
                    Upvalues:
                        [1] = u90
                    --]]
                    p99.UICorner.CornerRadius = u90.UICorner.CornerRadius
                    p99.UIStroke.Thickness = 3
                end).Parent = u90
            end
        end)
        task.delay(5 - u85.Time - 0.25, function() --[[Anonymous function at line 386]]
            --[[
            Upvalues:
                [1] = u91
                [2] = u4
                [3] = u90
                [4] = u85
            --]]
            if not u91 then
                u4:Create(u90, u85, {
                    ["Position"] = UDim2.new(0.5, 0, 0, -10),
                    ["AnchorPoint"] = Vector2.new(0.5, 1)
                }):Play()
            end
        end)
        return v92
    end)
end
u21.Event("ClanInvite"):Connect(function(p101, p102) --[[Anonymous function at line 401]]
    --[[
    Upvalues:
        [1] = u100
    --]]
    u100(p101, p102)
end)
local v103 = u44.NoTeam.NewTeam
local u104 = v103.TextBox
local u105 = v103.Create.Button
local function u106() --[[Anonymous function at line 415]]
    --[[
    Upvalues:
        [1] = u104
    --]]
    u104.Text = ""
    u104:ReleaseFocus()
end
u44.NoTeam:GetPropertyChangedSignal("Visible"):Connect(function() --[[Anonymous function at line 420]]
    --[[
    Upvalues:
        [1] = u44
        [2] = u106
    --]]
    if not u44.NoTeam.Visible then
        u106()
    end
end)
u9:BindOnClosed("Competitive", u106)
local function v115() --[[Anonymous function at line 428]]
    --[[
    Upvalues:
        [1] = u104
        [2] = u19
        [3] = u105
    --]]
    local v107 = u104.Text
    local v108 = u19.Clan.MaxNameLength
    local v109 = string.sub(v107, 1, v108)
    local v110 = string.gsub(v109, "[^%s%w]", "")
    local v111 = string.gsub(v110, "%s", " ")
    local v112 = string.gsub(v111, "%s%s+", " ")
    local v113 = string.match(v112, "^%s*(.*)") or ""
    u104.Text = v113
    local v114 = #v113 == 0 and true or string.match(v113, "^%s*(.-)%s*$") ~= v113
    u105.BackgroundTransparency = v114 and 0.5 or 0
    u105.UIStroke.Transparency = v114 and 0.75 or 0
    u105.Label.TextTransparency = v114 and 0.25 or 0
    u105.Label.UIStroke.Transparency = v114 and 0.75 or 0
    u105.Active = not v114
    u105.Selectable = not v114
end
u104:GetPropertyChangedSignal("Text"):Connect(v115)
v115()
u6(u105, u7.Button.Default)
u8(u105, function() --[[Anonymous function at line 451]]
    --[[
    Upvalues:
        [1] = u104
        [2] = u106
        [3] = u25
        [4] = u6
        [5] = u7
        [6] = u8
        [7] = u21
        [8] = u26
    --]]
    local u116 = u104.Text
    u106()
    u26(u25.new("Create Team"):Text("Are you sure you want to create a team? You can only create a team once every 12 hours.\n\nNote: you are not allowed to advertise in your team name. Breaking this rule or any other will disqualify your team."):Button(function(p117, u118) --[[Anonymous function at line 457]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u7
            [3] = u8
            [4] = u21
            [5] = u116
        --]]
        p117.Label.Text = "Yes"
        u6(p117, u7.Button.Green)
        u8(p117, function() --[[Anonymous function at line 460]]
            --[[
            Upvalues:
                [1] = u21
                [2] = u116
                [3] = u118
            --]]
            u21:FireServer("ClanCreate", u116)
            u118()
        end)
    end):Button(function(p119, p120) --[[Anonymous function at line 465]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u7
            [3] = u8
        --]]
        p119.Label.Text = "No"
        u6(p119, u7.Button.Red)
        u8(p119, p120)
    end):Build())
end)
local function u145() --[[Anonymous function at line 476]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u44
        [3] = u45
        [4] = u31
        [5] = u34
        [6] = u3
        [7] = u30
        [8] = u49
        [9] = u20
        [10] = u19
        [11] = u50
        [12] = u46
        [13] = u18
    --]]
    local v121 = u11:Get()
    if not v121 then
        return
    end
    local v122 = v121.Competitive
    local v123
    if v122 then
        v123 = v122.Clan
    else
        v123 = nil
    end
    if not (v122 and v123) then
        u44.NoTeam.Visible = true
        u45.Visible = false
        return
    end
    u44.NoTeam.Visible = false
    u45.Visible = true
    local v124 = v123.Members[1].UserId == u31.UserId
    local v125 = 0
    u45.NameLabel.Text = v123.Id
    local v126 = u34:GetAttribute("Ranks")
    if v126 then
        local v127 = nil
        for v128, v129 in u3:JSONDecode(v126) do
            if v129.id == v123.Id then
                v127 = v128
                break
            end
        end
        u45.Rank.Text = not v127 and ">500th" or u30(v127)
    else
        u45.Rank.Text = "Loading"
    end
    for v130, v131 in v123.Members do
        local v132 = v131.Score
        if v131.UserId == u31.UserId then
            v132 = v122.Score
        end
        v125 = v125 + v132
        local v133 = u49[v130]
        v133.Main.Info.Text = ("%* (@%*)"):format(v131.DisplayName, v131.Username)
        v133.Main.Score.Text = u20(v132)
        v133.Main.Icon.Image = ("rbxthumb://type=AvatarBust&id=%*&w=60&h=60"):format(v131.UserId)
        local v134
        if v124 then
            v134 = #v123.Members > 1
        else
            v134 = v124
        end
        v133.Main.Size = UDim2.new(1, v134 and -115 or 0, 1, 0)
        local v135 = v133.Kick
        local v136
        if v124 then
            v136 = v130 > 1
        else
            v136 = v124
        end
        v135.Visible = v136
        v133.Visible = true
    end
    for v137 = #v123.Members + 1, u19.Clan.MaxMembers do
        u49[v137].Visible = false
    end
    for v138, v139 in u50 do
        local v140
        if v138 == #v123.Members + 1 then
            v140 = v138 < u19.Clan.MaxMembers
        else
            v140 = false
        end
        v139.Visible = v140
    end
    local v141 = u46.Invite
    if #v123.Members >= u19.Clan.MaxMembers then
        v124 = false
    end
    v141.Visible = v124
    u45.Total.Text = u20(v125)
    u45.TrayButtons.Leave.Visible = #v123.Members > 1
    u45.TrayBut  tons.Delete.Visible = #v123.Members <= 1
    local v142 = u18.now() - v123.LastUpdated
    local v143 = math.max(0, v142)
    local v144 = v143 <= 60 and "just now" or (v143 <= 120 and "a minute ago" or (v143 <= 300 and "a few minutes ago" or (v143 <= 600 and "5+ minutes ago" or nil)))
    u45.LastUpdated.Text = ("Last updated: %*"):format(v144)
end
u9:BindOnOpened("Competitive", u145)
u34:GetAttributeChangedSignal("Ranks"):Connect(u145)
local function u152() --[[Anonymous function at line 567]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u10
        [3] = u18
        [4] = u33
    --]]
    local v146 = u11:Get()
    if v146 then
        local v147 = v146.Competitive
        local v148 = u10:GetSeason()
        if v147 and v148 then
            local v149 = u18.now()
            local v150 = u10.Seasons[v148].FinishTime.UnixTimestamp - v149
            local v151 = math.max(0, v150)
            u33.Top.Timer.Label.Text = u18.formatRealTime(v151)
        end
    else
        return
    end
end
task.spawn(function() --[[Anonymous function at line 586]]
    --[[
    Upvalues:
        [1] = u9
        [2] = u152
    --]]
    while true do
        if u9:IsOpen("Competitive") then
            u152()
        end
        task.wait(1)
    end
end)
u9:BindOnOpened("Competitive", u152)
local function v175() --[[Anonymous function at line 599]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u33
        [3] = u13
        [4] = u10
        [5] = u152
        [6] = u51
        [7] = u14
        [8] = u16
        [9] = u17
        [10] = u28
        [11] = u40
        [12] = u6
        [13] = u7
        [14] = u20
        [15] = u32
        [16] = u145
    --]]
    local v153 = u11:Get()
    if v153 then
        local v154 = v153.Competitive
        u33.Top.Stars.Bar.Label.Text = u13(v154 and (v154.Score or 0) or 0)
        local v155 = u10:GetSeason()
        if v154 and v155 then
            u33.Content.Visible = true
            u33.Sidebar.Visible = true
            u33.Top.Visible = true
            u33.NoSeason.Visible = false
            u152()
            local v156 = u10.Seasons[v155]
            for v157, v158 in u51 do
                local v159 = table.find(v154.Claimed, v157) ~= nil
                local v160 = v159 and 0.5 or 0
                v158.Claimed.Visible = v159
                v158.Cover.Visible = v154.Score < u10:GetRequirement(v156, v157)
                local v161 = { "Frame" }
                u14(v158.Option1, v160, nil, v161)
                u14(v158.Option2, v160, nil, v161)
                u14(v158.Label, v160, nil, v161)
            end
            local v162 = u10:TrackComplete(v153)
            local v163 = u33.Content.Rewards.Last
            v163.Cover.Visible = not v162
            local v164 = v154.Score % u10.Bonus.Every
            local v165 = not u10:TrackComplete(v153) and 0 or v164
            v163.Bar.Fill.Size = UDim2.new(v165 / u10.Bonus.Every, 0, 1, 6)
            v163.Bar.Label.Text = ("%* / %*"):format(v165, u10.Bonus.Every)
            v163.Bar.Fill.Visible = v165 > 0
            v163.Info.Text = ("Earn eggs for every %* additional stars you get!"):format(u10.Bonus.Every)
            v163.Frame.Icon.Image = u16[u10.Bonus.Item.Name].Icon
            u17:FormatAmount(v163.Frame.Label, u10.Bonus.Item)
            local v166 = u17:GetOwnedAmount(v153, u10.RerollCost) > u17:GetAmount(u10.RerollCost)
            for v167 = 1, 4 do
                local v168 = u28:FindById(v153, (("%*-%*"):format(u10.QuestKey, v167)))
                if v168 then
                    local v169 = v168.Progress[1]
                    local v170 = u28:GetRequirement(v168.Tasks[1])
                    local u171 = u40:Get()
                    u6(u171.Content.Reroll.Button, v166 and u7.Button.Pink or u7.Button.Red)
                    u171.Content.Type.Text = v167 <= 2 and "Permanent" or "Repeatable"
                    u171.Content.Type.TextColor3 = v167 <= 2 and u7.Text.Green or u7.Text.Pink
                    u171.Content.Reroll.Visible = v167 > 2
                    u171.Content.Reroll.Button.Amount.Text = u17:GetAmount(u10.RerollCost)
                    u171.Content.Reward.Label.Text = u20(u17:GetAmount(v168.Rewards[1]))
                    task.defer(function() --[[Anonymous function at line 674]]
                        --[[
                        Upvalues:
                            [1] = u171
                            [2] = u32
                        --]]
                        u171.Content.Reward.Label.Size = UDim2.new(0, u171.Content.Reward.Label.TextBounds.X / u32.UIScale.Scale, 1, 0)
                    end)
                    u171.LayoutOrder = v167
                    u171.Content.Bar.Fill.Size = UDim2.new(v169 / v170, 0, 1, 6)
                    u171.Content.Label.Text = u28:FormatTask(v168.Tasks[1])
                    local v172 = u171.Content.Bar.Label
                    local v173 = v169 / v170 * 100
                    v172.Text = ("%*%%"):format((math.ceil(v173)))
                    u171.Parent = u33.Content.Tasks
                    u28:UpdateIcon(u171.Content.Icon, v168)
                    u171.Content.Icon.Size = UDim2.fromOffset(150, 150)
                    u171.Content.Icon.Label.Size = UDim2.fromScale(1, 1)
                end
            end
            u40:Done()
            local v174 = u33.Content.Rewards.UIPadding.PaddingLeft.Offset + u33.Content.Rewards.UIPadding.PaddingRight.Offset
            u33.Content.Rewards.CanvasSize = UDim2.new(0, u33.Content.Rewards.UIListLayout.AbsoluteContentSize.X / u32.UIScale.Scale + v174, 0, 0)
            u145()
        else
            u33.Content.Visible = false
            u33.Sidebar.Visible = false
            u33.Top.Visible = false
            u33.NoSeason.Visible = true
        end
    else
        return
    end
end
local v176 = u10:GetSeason()
local u177
if v176 then
    local v178 = u10.Seasons[v176]
    u177 = u106
    for u179, v180 in v178.Rewards do
        local u181 = v35:Clone()
        u181.LayoutOrder = u179
        u181.Cover.Visible = true
        u181.Cover.Requirement.Label.Text = u13(u10:GetRequirement(v178, u179))
        u181.Parent = u33.Content.Rewards
        local function v183() --[[Anonymous function at line 714]]
            --[[
            Upvalues:
                [1] = u181
            --]]
            local v182
            if u181.Cover.Visible == false then
                v182 = u181.Claimed.Visible == false
            else
                v182 = false
            end
            u181.Option1.Label.Visible = v182
            u181.Option1.Action.Visible = v182
            u181.Option2.Action.Visible = v182
        end
        local v184
        if u181.Cover.Visible == false then
            v184 = u181.Claimed.Visible == false
        else
            v184 = false
        end
        u181.Option1.Label.Visible = v184
        u181.Option1.Action.Visible = v184
        u181.Option2.Action.Visible = v184
        u181.Cover:GetPropertyChangedSignal("Visible"):Connect(v183)
        u181.Claimed:GetPropertyChangedSignal("Visible"):Connect(v183)
        for u185, v186 in v180 do
            local v187 = u181:FindFirstChild((("Option%*"):format(u185)))
            local v188 = v187.Button
            local v189 = v12(v186, true)
            v189.Size = UDim2.fromScale(1, 1)
            v189.Button.Inner.Label.TextSize = 28
            v189.Parent = v188
            u6(v187.Action.Button, u7.Button.Purple)
            u8(v187.Action.Button, function() --[[Anonymous function at line 734]]
                --[[
                Upvalues:
                    [1] = u21
                    [2] = u179
                    [3] = u185
                --]]
                u21:FireServer("ClaimCompetitivePrize", u179, u185)
            end)
        end
        task.defer(function() --[[Anonymous function at line 739]]
            --[[
            Upvalues:
                [1] = u181
                [2] = u32
            --]]
            u181.Cover.Requirement.Label.Size = UDim2.fromOffset(u181.Cover.Requirement.Label.TextBounds.X / u32.UIScale.Scale, 40)
        end)
        u51[u179] = u181
    end
else
    u177 = u106
end
for _, u190 in u33.Sidebar.Buttons:GetChildren() do
    if u190:IsA("Frame") then
        u8(u190.Button, function() --[[Anonymous function at line 750]]
            --[[
            Upvalues:
                [1] = u190
                [2] = u33
                [3] = u177
            --]]
            local v191 = u190.Name
            for _, v192 in u33.Content:GetChildren() do
                v192.Visible = v192.Name == v191
            end
            u177()
        end)
    end
end
for v193, v194 in u10.Placements do
    local v195 = u33.Content.Top500.Tiers:FindFirstChild((("Tier%*"):format(v193)))
    if v195 then
        for v196, v197 in v194 do
            local v198 = v12(v197, true)
            v198.Size = UDim2.fromOffset(125, 125)
            v198.LayoutOrder = v196
            v198.Parent = v195.Items
        end
    end
end
u11:ConnectDataChanged({
    "Competitive",
    "Quests",
    "QuestsCompleted",
    "Powerups"
}, v175)
u11:ConnectDataChanged("AreasUnlocked", function(p199) --[[Anonymous function at line 774]]
    --[[
    Upvalues:
        [1] = u32
    --]]
    u32.HUD.Right.Competitive.Visible = p199.AreasUnlocked.Zen == true
end)
u33.Content.Rewards.UIListLayout:GetPropertyChangedSignal("AbsoluteContentSize"):Connect(v175)
if u1.LocalPlayer.UserId == 941072 then
    u1.LocalPlayer:GetMouse().KeyDown:Connect(function(p200) --[[Anonymous function at line 784]]
        --[[
        Upvalues:
            [1] = u9
        --]]
        if p200 == "z" then
            u9:Toggle("Competitive")
        end
    end)
end
u6(u33.Close.Button, u7.Button.Red)
u8(u33.Close.Button, function() --[[Anonymous function at line 793]]
    --[[
    Upvalues:
        [1] = u9
    --]]
    u9:Close()
end)
for _, v201 in u33.Content:GetChildren() do
    v201.Visible = v201.Name == "Rewards"
end
return {}
