-- ... (Keep the existing code before the Enchant module) ...

-- NEW: Enchant Module and UI
local Enchant = {}
local EnchantUtil = require(replicatedStorage.Shared.Utils.EnchantUtil)
local PetsData = require(replicatedStorage.Shared.Data.Pets)
local EnchantsData = require(replicatedStorage.Shared.Data.Enchants)
local ItemUtil = require(replicatedStorage.Shared.Utils.Stats.ItemUtil)
local FormatRomanNumeral = require(replicatedStorage.Shared.Framework.Utilities.String.FormatRomanNumeral)
local FormatCommas = require(replicatedStorage.Shared.Framework.Utilities.String.FormatCommas)

do
    -- MODIFIED FUNCTION: Get only equipped, enchantable pets
    local function GetEnchantablePets()
        local playerData = Data.GetData()
        if not playerData or not playerData.Pets or not playerData.Pets.All or not playerData.Teams then return {} end

        -- 1. Get all unique equipped pet IDs from all teams
        local equippedPetIds = {}
        if playerData.Teams and type(playerData.Teams) == "table" then
            for _, teamData in ipairs(playerData.Teams) do
                if teamData and teamData.Pets and type(teamData.Pets) == "table" then
                    for _, petId in ipairs(teamData.Pets) do
                        if type(petId) == "string" then
                            equippedPetIds[petId] = true -- Use a set for uniqueness
                        end
                    end
                end
            end
        end

        -- Fallback or supplement with Pets.Equipped if Teams isn't the primary source (adjust if needed based on game logic)
        --[[
        if playerData.Pets.Equipped and type(playerData.Pets.Equipped) == "table" then
            for _, petId in ipairs(playerData.Pets.Equipped) do
                 if type(petId) == "string" then
                    equippedPetIds[petId] = true
                end
            end
        end
        --]]

        local enchantablePetsList = {}
        -- 2. Iterate through all pets and filter
        for _, petData in ipairs(playerData.Pets.All) do
            -- Check if equipped AND enchantable
            if equippedPetIds[petData.Id] then
                local isEnchantableSuccess, isEnchantable = pcall(EnchantUtil.IsEnchantable, EnchantUtil, petData) -- Pass self if it's a method

                if isEnchantableSuccess and isEnchantable then
                    -- Format display name
                    local petInfo = PetsData[petData.Name]
                    local displayName = petData.Name
                    if petData.Shiny then displayName = "Shiny " .. displayName end
                    displayName = displayName .. " (ID: " .. string.sub(petData.Id, 1, 4) .. ")" -- Short ID for display

                    table.insert(enchantablePetsList, {
                        DisplayName = displayName,
                        Value = petData.Id -- Store the Pet ID as the value
                    })
                end
            end
        end

        -- Sort pets (optional, e.g., alphabetically by display name)
        table.sort(enchantablePetsList, function(a, b) return a.DisplayName < b.DisplayName end)

        return enchantablePetsList
    end
    Enchant.GetEnchantablePets = GetEnchantablePets -- Expose the function

    -- ... (Keep GetAllEnchantsWithLevels, ParseEnchantDisplayName, HasDesiredEnchant, GetRerollCost, AutoEnchantLoop, GetCurrentStatus functions) ...
    local function GetAllEnchantsWithLevels()
        local allEnchants = {}
        for enchantId, enchantInfo in pairs(EnchantsData) do
            for level = 1, enchantInfo.Levels do
                 local displayName = ("%s (Level %s)"):format(enchantInfo.DisplayName, FormatRomanNumeral(level))
                table.insert(allEnchants, displayName)
            end
        end
        table.sort(allEnchants)
        return allEnchants
    end
    Enchant.GetAllEnchantsWithLevels = GetAllEnchantsWithLevels

    local function ParseEnchantDisplayName(displayName)
        local namePart, levelPart = displayName:match("(.+) %(Level (%S+)%)")
        if not namePart or not levelPart then return nil, nil end

        local foundId = nil
        local foundLevel = nil

        for enchantId, enchantInfo in pairs(EnchantsData) do
            if enchantInfo.DisplayName == namePart then
                foundId = enchantId
                break
            end
        end

        local romanMap = { I = 1, II = 2, III = 3, IV = 4, V = 5 }
        foundLevel = romanMap[levelPart]

        if not foundId or not foundLevel then
            warn("Could not parse enchant display name:", displayName)
            return nil, nil
        end

        return foundId, foundLevel
    end
    Enchant.ParseEnchantDisplayName = ParseEnchantDisplayName


    local function HasDesiredEnchant(pet, desiredEnchantsMap)
        if not pet or not pet.Enchants then return false end

        for _, currentEnchant in ipairs(pet.Enchants) do
            local enchantId = currentEnchant.Id
            local enchantLevel = currentEnchant.Level

            -- Check if this specific enchant ID is desired
            if desiredEnchantsMap[enchantId] then
                -- Check if the pet's level meets or exceeds the desired level for this enchant
                if enchantLevel >= desiredEnchantsMap[enchantId] then
                    return true, enchantId, enchantLevel -- Return true and the found enchant
                end
            end
        end
        return false
    end
    Enchant.HasDesiredEnchant = HasDesiredEnchant

    local function GetRerollCost(pet)
         if not pet or not PetsData[pet.Name] then return nil end
         local petInfo = PetsData[pet.Name]
         local petRarity = petInfo.Rarity
         local costCategory = petRarity == "Legendary" and (petInfo.Tier or 1) == 1 and "EasyLegendary" or petRarity
         local costAmount = Constants.EnchantsRerollAllGemCosts[costCategory]
         if not costAmount then
             warn("Could not find reroll cost for category:", costCategory, "Pet:", pet.Name)
             return nil
         end
         return { Type = "Currency", Currency = "Gems", Amount = costAmount }
    end
    Enchant.GetRerollCost = GetRerollCost

    local autoEnchantRunning = false
    local currentStatus = "Idle"
    local function AutoEnchantLoop()
        if autoEnchantRunning then return end
        autoEnchantRunning = true
        currentStatus = "Starting..."

        local petGemSpendTracker = {} -- Track gems spent per pet ID { [petId] = spentAmount }

        while _G.Settings.EnchantSettings.Enabled and next(_G.Settings.EnchantSettings.SelectedPetIds) do -- Check if table is not empty
            local petIdsToProcess = {}
            for id, _ in pairs(_G.Settings.EnchantSettings.SelectedPetIds) do table.insert(petIdsToProcess, id) end

            if #petIdsToProcess == 0 then
                 currentStatus = "No pets selected."
                 break
            end

            local processedAPet = false
            local needRefreshUI = false -- Flag to refresh dropdown UI after ID changes

            for i = #petIdsToProcess, 1, -1 do -- Iterate backwards for safe removal/ID update
                local petId = petIdsToProcess[i]
                if not _G.Settings.EnchantSettings.Enabled then break end
                if not _G.Settings.EnchantSettings.SelectedPetIds[petId] then continue end

                task.wait(0.2)

                local playerData = Data.GetData()
                if not playerData then
                    currentStatus = "Waiting for player data..."
                    task.wait(1)
                    break
                end

                local currentPet = nil
                for _, p in ipairs(playerData.Pets.All) do
                    if p.Id == petId then
                        currentPet = p
                        break
                    end
                end

                if not currentPet then
                    currentStatus = ("Pet %s not found. Removing."):format(string.sub(petId, 1, 4))
                    _G.Settings.EnchantSettings.SelectedPetIds[petId] = nil
                    petGemSpendTracker[petId] = nil -- Clear tracker too
                    needRefreshUI = true
                    Library:Notify({ Title = "Auto Enchant", Description = currentStatus, Time = 3 })
                    continue
                end

                local desiredEnchantsMap = {}
                for displayName, selected in pairs(_G.Settings.EnchantSettings.DesiredEnchants) do
                    if selected then
                        local id, level = ParseEnchantDisplayName(displayName)
                        if id and level then
                            desiredEnchantsMap[id] = math.max(desiredEnchantsMap[id] or 0, level)
                        end
                    end
                end

                 if not next(desiredEnchantsMap) then
                     currentStatus = "No desired enchants selected."
                     _G.Settings.EnchantSettings.Enabled = false
                     needRefreshUI = true
                     Library:Notify({ Title = "Auto Enchant", Description = currentStatus, Time = 3 })
                     break
                 end

                local hasDesired, foundId, foundLevel = HasDesiredEnchant(currentPet, desiredEnchantsMap)
                if hasDesired and _G.Settings.EnchantSettings.StopOnAnyDesired then
                    local enchantInfo = EnchantsData[foundId]
                    local statusMsg = ("Pet %s has %s (Lv %s). Stopping for this pet."):format(currentPet.Name, enchantInfo.DisplayName, FormatRomanNumeral(foundLevel))
                    currentStatus = statusMsg
                    _G.Settings.EnchantSettings.SelectedPetIds[petId] = nil
                    petGemSpendTracker[petId] = nil
                    needRefreshUI = true
                    Library:Notify({ Title = "Auto Enchant", Description = statusMsg, Time = 4 })
                    continue
                end

                local rerollCost = GetRerollCost(currentPet)
                if not rerollCost then
                    currentStatus = ("Cost error for %s. Skipping."):format(currentPet.Name)
                    Library:Notify({ Title = "Auto Enchant Error", Description = currentStatus, Time = 3 })
                    _G.Settings.EnchantSettings.SelectedPetIds[petId] = nil
                    petGemSpendTracker[petId] = nil
                    needRefreshUI = true
                    continue
                end

                local maxGems = _G.Settings.EnchantSettings.MaxGemsPerPet
                local spentOnPet = petGemSpendTracker[petId] or 0

                if maxGems >= 0 and (spentOnPet + rerollCost.Amount > maxGems) then
                     currentStatus = ("Gem limit (%sG/%sG) reached for %s. Stopping for this pet.")
                        :format(FormatCommas(spentOnPet), FormatCommas(maxGems), currentPet.Name)
                     Library:Notify({ Title = "Auto Enchant Limit", Description = currentStatus, Time = 5 })
                     _G.Settings.EnchantSettings.SelectedPetIds[petId] = nil
                     petGemSpendTracker[petId] = nil
                     needRefreshUI = true
                     continue
                 end

                local canAfford = ItemUtil:CanAfford(playerData, rerollCost)
                if not canAfford then
                    currentStatus = ("Need %s Gems for %s. Waiting...")
                        :format(FormatCommas(rerollCost.Amount), currentPet.Name)
                    task.wait(1)
                    continue -- Keep trying this pet later
                end

                currentStatus = ("Rerolling %s (%s Gems)..."):format(currentPet.Name, FormatCommas(rerollCost.Amount))
                processedAPet = true

                local success, result = pcall(InvokeNetwork, "RerollEnchants", petId)

                if not success then
                    currentStatus = ("Error rerolling %s: %s"):format(currentPet.Name, tostring(result):sub(1, 50))
                    Library:Notify({ Title = "Auto Enchant Error", Description = currentStatus, Time = 4 })
                    task.wait(1)
                    continue
                end

                local newPetId = result
                if not newPetId or type(newPetId) ~= "string" then
                     currentStatus = ("Reroll fail for %s (invalid response)."):format(currentPet.Name)
                     Library:Notify({ Title = "Auto Enchant Error", Description = currentStatus, Time = 4 })
                     task.wait(0.5)
                     continue
                end

                 -- Update gem tracker
                 petGemSpendTracker[petId] = (petGemSpendTracker[petId] or 0) + rerollCost.Amount

                 if newPetId ~= petId then
                     currentStatus = ("Pet ID %s -> %s."):format(string.sub(petId, 1, 4), string.sub(newPetId, 1, 4))
                     _G.Settings.EnchantSettings.SelectedPetIds[petId] = nil
                     _G.Settings.EnchantSettings.SelectedPetIds[newPetId] = true
                     -- Transfer spent gems tracker to new ID
                     petGemSpendTracker[newPetId] = petGemSpendTracker[petId]
                     petGemSpendTracker[petId] = nil
                     petId = newPetId -- Use the new ID going forward in this iteration
                     needRefreshUI = true
                     Library:Notify({ Title = "Auto Enchant", Description = currentStatus, Time = 3 })
                     task.wait(0.1)
                 end

                 playerData = Data.GetData() -- Refresh data again
                 local rerolledPet = nil
                 for _, p in ipairs(playerData.Pets.All) do
                    if p.Id == petId then
                        rerolledPet = p
                        break
                    end
                 end

                 if rerolledPet then
                    local hasDesiredAfter, foundIdAfter, foundLevelAfter = HasDesiredEnchant(rerolledPet, desiredEnchantsMap)
                    if hasDesiredAfter then
                        local enchantInfo = EnchantsData[foundIdAfter]
                        local successMsg = ("Found %s (Lv %s) on %s!")
                            :format(enchantInfo.DisplayName, FormatRomanNumeral(foundLevelAfter), rerolledPet.Name)
                        currentStatus = successMsg
                        Library:Notify({ Title = "Auto Enchant Success!", Description = successMsg, Time = 5, Color = Color3.fromRGB(0, 255, 0) })
                        if _G.Settings.EnchantSettings.StopOnAnyDesired then
                             _G.Settings.EnchantSettings.SelectedPetIds[petId] = nil
                             petGemSpendTracker[petId] = nil
                             needRefreshUI = true
                        end
                    else
                        currentStatus = ("Rerolled %s. No desired enchant yet."):format(rerolledPet.Name)
                    end
                 else
                     currentStatus = ("Cannot find pet %s after reroll."):format(currentPet.Name)
                     Library:Notify({ Title = "Auto Enchant Warning", Description = currentStatus, Time = 3 })
                     _G.Settings.EnchantSettings.SelectedPetIds[petId] = nil -- Remove if lost track
                     petGemSpendTracker[petId] = nil
                     needRefreshUI = true
                 end
            end -- End of pet loop

            if needRefreshUI then
                 -- Update the dropdown UI outside the inner loop if IDs changed or pets were removed
                 task.spawn(function()
                     local petList = Enchant.GetEnchantablePets()
                     local currentValues = {}
                     local currentSelection = {}
                     for _, petInfo in ipairs(petList) do
                         table.insert(currentValues, petInfo.DisplayName)
                         if _G.Settings.EnchantSettings.SelectedPetIds[petInfo.Value] then
                             currentSelection[petInfo.DisplayName] = true
                         end
                     end
                     if PetSelectorDropdown then -- Check if UI still exists
                        PetSelectorDropdown:SetValues(currentValues)
                        PetSelectorDropdown:SetValue(currentSelection)
                     end
                 end)
            end

            if not processedAPet and _G.Settings.EnchantSettings.Enabled and next(_G.Settings.EnchantSettings.SelectedPetIds) then
                 currentStatus = "Idle (Waiting for Gems or conditions)."
                 task.wait(2)
            end

        end -- End of while loop

        if not _G.Settings.EnchantSettings.Enabled then
            currentStatus = "Disabled."
        elseif not next(_G.Settings.EnchantSettings.SelectedPetIds) then
             currentStatus = "Finished (No pets remaining)."
        end
        autoEnchantRunning = false
    end
    Enchant.AutoEnchantLoop = AutoEnchantLoop
    Enchant.GetCurrentStatus = function() return currentStatus end

end -- End of Enchant Module

-- ... (Keep existing UI loading section) ...

-- Add Enchant Box to Main Tab
local EnchantBox = MainTab:AddLeftGroupbox("Auto Enchanter")

-- Initialize dropdown values using the NEW function
local enchantablePets = Enchant.GetEnchantablePets()
local petDropdownValues = {}
for _, petInfo in ipairs(enchantablePets) do
    table.insert(petDropdownValues, petInfo.DisplayName)
end

local PetSelectorDropdown = EnchantBox:AddDropdown("SelectedPetsToEnchant", {
    Text = "Select Equipped Pets",
    Tooltip = "Select equipped pets to automatically reroll enchants on",
    Values = petDropdownValues, -- Use the filtered list
    Multi = true,
    Searchable = true,
    MaxVisibleDropdownItems = 6,
    Default = {},
    Callback = function(selectedDisplayNames)
        _G.Settings.EnchantSettings.SelectedPetIds = {}
        local currentEnchantable = Enchant.GetEnchantablePets() -- Get fresh filtered list with IDs

        for displayName, selected in pairs(selectedDisplayNames) do
            if selected then
                for _, petInfo in ipairs(currentEnchantable) do
                     if petInfo.DisplayName == displayName then
                        _G.Settings.EnchantSettings.SelectedPetIds[petInfo.Value] = true
                        break
                    end
                end
            end
        end
        -- No need for the task.spawn refresh here anymore, button handles it.
    end
})

-- ADDED REFRESH BUTTON *UNDER* THE DROPDOWN
local RefreshPetListButton = EnchantBox:AddButton({
    Text = "Refresh Pet List",
    Tooltip = "Update the list of equipped, enchantable pets",
    Func = function()
         local newPetList = Enchant.GetEnchantablePets() -- Use the updated function
         local newValues = {}
         local currentSelection = {}
         for _, petInfo in ipairs(newPetList) do
             table.insert(newValues, petInfo.DisplayName)
             -- Preserve selection based on ID
             if _G.Settings.EnchantSettings.SelectedPetIds[petInfo.Value] then
                 currentSelection[petInfo.DisplayName] = true
             end
         end
         PetSelectorDropdown:SetValues(newValues)
         PetSelectorDropdown:SetValue(currentSelection) -- Re-apply selection
         Library:Notify("Equipped pet list refreshed.")
     end
})

-- ... (Keep the rest of the EnchantBox UI: DesiredEnchants dropdown, Toggles, Label, etc.) ...
local allEnchants = Enchant.GetAllEnchantsWithLevels()
local EnchantSelectorDropdown = EnchantBox:AddDropdown("DesiredEnchants", {
    Text = "Desired Enchants",
    Tooltip = "Select desired enchants (highest level per type)",
    Values = allEnchants,
    Multi = true,
    Searchable = true,
    MaxVisibleDropdownItems = 6,
    Default = _G.Settings.EnchantSettings.DesiredEnchants,
    Callback = function(Value)
        _G.Settings.EnchantSettings.DesiredEnchants = Value
    end
})

local StopOnAnyToggle = EnchantBox:AddToggle("StopOnAnyDesired", {
    Text = "Stop on Any Desired",
    Tooltip = "Stop enchanting a pet once *any* of the desired enchants is found on it",
    Default = _G.Settings.EnchantSettings.StopOnAnyDesired,
    Callback = function(Value)
        _G.Settings.EnchantSettings.StopOnAnyDesired = Value
    end
})

local MaxGemsInput = EnchantBox:AddInput("MaxGemsPerPetInput", {
    Text = "Max Gems / Pet (-1=Inf)",
    Tooltip = "Maximum Gems to spend per pet before stopping (-1 for unlimited)",
    Default = tostring(_G.Settings.EnchantSettings.MaxGemsPerPet),
    Numeric = true,
    Finished = true, -- Only update value on Enter or focus lost
    AllowEmpty = false,
    Placeholder = "-1",
    Callback = function(Value)
        local num = tonumber(Value)
        _G.Settings.EnchantSettings.MaxGemsPerPet = (num and num >= -1) and num or -1
        -- Update the input visually in case it was invalid or empty
        if tostring(_G.Settings.EnchantSettings.MaxGemsPerPet) ~= Value then
             MaxGemsInput:SetValue(tostring(_G.Settings.EnchantSettings.MaxGemsPerPet))
        end
    end
})


local EnchantStatusLabel = EnchantBox:AddLabel("Status: Idle")

local AutoEnchantToggle = EnchantBox:AddToggle("AutoEnchant", {
    Text = "Enable Auto Enchant",
    Tooltip = "Automatically reroll selected pets for desired enchants",
    Default = _G.Settings.EnchantSettings.Enabled,
    Callback = function(Value)
        _G.Settings.EnchantSettings.Enabled = Value
        if Value then
            -- Start the loop only if not already running
            if not autoEnchantRunning then
                task.spawn(Enchant.AutoEnchantLoop)
            end
        else
            EnchantStatusLabel:SetText("Status: Disabling...") -- Indicate stopping
            -- Loop checks _G.Settings.EnchantSettings.Enabled and will stop
        end
    end
})


-- Update status label periodically
task.spawn(function()
    while task.wait(0.5) do
         if Window and Window.Visible and EnchantStatusLabel and EnchantStatusLabel.Visible then -- Add checks
            EnchantStatusLabel:SetText("Status: " .. Enchant.GetCurrentStatus())
        end
    end
end)


-- ... (Keep the rest of the script: Theme setup, Anti-AFK, final Notify) ...