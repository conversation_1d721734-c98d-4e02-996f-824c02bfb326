{"firesignal": "[Function]", "makefolder": "[Function]", "clonefunction": "[Function]", "detourfunction": "[Function]", "cansignalreplicate": "[Function]", "isscriptable": "[Function]", "rconsolesettitle": "[Function]", "hookmetamethod": "[Function]", "consolecreate": "[Function]", "isnewcclosure": "[Function]", "checkparallel": "[Function]", "consoledestroy": "[Function]", "isgameactive": "[Function]", "gethiddenprop": "[Function]", "setfflag": "[Function]", "getcallingscript": "[Function]", "setthreadcontext": "[Function]", "getallthreads": "[Function]", "mouse1press": "[Function]", "toclipboard": "[Function]", "newcclosure": "[Function]", "request": "[Function]", "getthreadcontext": "[Function]", "isfunctionhooked": "[Function]", "shared": {}, "replacefunction": "[Function]", "cloneref": "[Function]", "setscriptable": "[Function]", "getmodules": "[Function]", "http_request": "[Function]", "isexecutorclosure": "[Function]", "isourclosure": "[Function]", "isfile": "[Function]", "mouse2release": "[Function]", "inparallel": "[Function]", "getstack": "[Function]", "rconsolename": "[Function]", "getupvalue": "[Function]", "consolesettitle": "[Function]", "mousemoveabs": "[Function]", "base64decode": "[Function]", "mouse1click": "[Function]", "setupvalue": "[Function]", "isfolder": "[Function]", "create_comm_channel": "[Function]", "isrenderobj": "[Function]", "iscustomcclosure": "[Function]", "get_comm_channel": "[Function]", "getscripts": "[Function]", "getnilinstances": "[Function]", "dumpstring": "[Function]", "iscclosure": "[Function]", "restorefunction": "[Function]", "getinstancelist": "[Function]", "getupvalues": "[Function]", "rconsoleclear": "[Function]", "decompilesync": "[Function]", "getfunctionhash": "[Function]", "isreadonly": "[Function]", "base64encode": "[Function]", "getinfo": "[Function]", "sethiddenproperty": "[Function]", "writefile": "[Function]", "base64_encode": "[Function]", "loadfile": "[Function]", "getconstant": "[Function]", "filtergc": "[Function]", "getactors": "[Function]", "getconnections": "[Function]", "checkcaller": "[Function]", "rconsoledestroy": "[Function]", "getnspval": "[Function]", "setidentity": "[Function]", "setreadonly": "[Function]", "setthreadidentity": "[Function]", "getscriptfromthread": "[Function]", "setsimulationradius": "[Function]", "isrbxactive": "[Function]", "getfenv": "[Function]", "setrenderproperty": "[Function]", "getidentity": "[Function]", "WebSocket": {"Connect": "[Function]", "connect": "[Function]", "new": "[Function]", "New": "[Function]"}, "base64": {"encode": "[Function]", "decode": "[Function]"}, "setconstant": "[Function]", "set_thread_identity": "[Function]", "saveinstance": "[Function]", "getinstances": "[Function]", "getconstants": "[Function]", "firetouchinterest": "[Function]", "cache": {"replace": "[Function]", "iscached": "[Function]", "invalidate": "[Function]"}, "get_thread_identity": "[Function]", "_G": {}, "getthreadidentity": "[Function]", "isnetworkowner": "[Function]", "compareinstances": "[Function]", "Drawing": {"clear": "[Function]", "Fonts": {"UI": 0, "Monospace": 3, "Plex": 2, "System": 1}, "new": "[Function]"}, "queue_on_teleport": "[Function]", "delfile": "[Function]", "getrenderproperty": "[Function]", "mouse1release": "[Function]", "getscriptclosure": "[Function]", "gethui": "[Function]", "setnamecallmethod": "[Function]", "httppost": "[Function]", "fireproximityprompt": "[Function]", "getrenv": "[Function]", "getgenv": "[Function]", "getrunningscripts": "[Function]", "cleardrawcache": "[Function]", "getsenv": "[Function]", "getthreadobject": "[Function]", "decompile": "[Function]", "getcustomasset": "[Function]", "replaceclosure": "[Function]", "getnamecallmethod": "[Function]", "gethiddenproperty": "[Function]", "getobjects": "[Function]", "getproto": "[Function]", "getfpscap": "[Function]", "getregistry": "[Function]", "delfolder": "[Function]", "listfiles": "[Function]", "keyrelease": "[Function]", "setclipboard": "[Function]", "queueonteleport": "[Function]", "consoleinput": "[Function]", "getscripthash": "[Function]", "clearteleportqueue": "[Function]", "clear_teleport_queue": "[Function]", "keypress": "[Function]", "messagebox": "[Function]", "lz4decompress": "[Function]", "keytap": "[Function]", "base64_decode": "[Function]", "getexecutorname": "[Function]", "identifyexecutor": "[Function]", "setrawmetatable": "[Function]", "mousemoverel": "[Function]", "newlclosure": "[Function]", "readfile": "[Function]", "getscriptfunction": "[Function]", "islclosure": "[Function]", "getrawmetatable": "[Function]", "getconnection": "[Function]", "mouse2press": "[Function]", "getfflag": "[Function]", "debug": {"dumpheap": "[Function]", "getconstant": "[Function]", "getproto": "[Function]", "setmemorycategory": "[Function]", "profilebegin": "[Function]", "loadmodule": "[Function]", "traceback": "[Function]", "getstack": "[Function]", "getupvalues": "[Function]", "getupvalue": "[Function]", "getmemorycategory": "[Function]", "resetmemorycategory": "[Function]", "getregistry": "[Function]", "setstack": "[Function]", "setupvalue": "[Function]", "profileend": "[Function]", "info": "[Function]", "getinfo": "[Function]", "getprotos": "[Function]", "setconstant": "[Function]", "setname": "[Function]", "getconstants": "[Function]"}, "rconsoleprint": "[Function]", "getprotos": "[Function]", "crypt": {"encrypt": "[Function]", "base64": {"encode": "[Function]", "decode": "[Function]"}, "base64decode": "[Function]", "base64_decode": "[Function]", "base64encode": "[Function]", "generatebytes": "[Function]", "decrypt": "[Function]", "hash": "[Function]", "generatekey": "[Function]", "base64_encode": "[Function]"}, "lz4compress": "[Function]", "http": {"request": "[Function]"}, "appendfile": "[Function]", "getscriptbytecode": "[Function]", "consoleclear": "[Function]", "checkclosure": "[Function]", "getloadedmodules": "[Function]", "getgc": "[Function]", "isparallel": "[Function]", "getsimulationradius": "[Function]", "consoleprint": "[Function]", "setfpscap": "[Function]", "run_on_actor": "[Function]", "loadstring": "[Function]", "replicatesignal": "[Function]", "hookfunction": "[Function]", "mousescroll": "[Function]", "iswindowactive": "[Function]", "fireclickdetector": "[Function]", "rconsoleinput": "[Function]", "rconsolecreate": "[Function]", "getreg": "[Function]", "bit": {"bdiv": "[Function]", "rshift": "[Function]", "ror": "[Function]", "bnot": "[Function]", "bmul": "[Function]", "bsub": "[Function]", "rol": "[Function]", "tohex": "[Function]", "band": "[Function]", "bor": "[Function]", "badd": "[Function]", "bswap": "[Function]", "bxor": "[Function]", "tobit": "[Function]", "lshift": "[Function]", "arshift": "[Function]", "bpopcount": "[Function]"}, "mouse2click": "[Function]", "messageboxasync": "[Function]", "dofile": "[Function]", "consolename": "[Function]", "httpget": "[Function]", "setstack": "[Function]", "getcallbackvalue": "[Function]"}